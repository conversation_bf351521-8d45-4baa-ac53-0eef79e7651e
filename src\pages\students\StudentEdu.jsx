import React, { useEffect, useState, useRef, useContext, useCallback, useMemo } from 'react';
import { Box, Text, List, useNavigate, Modal, <PERSON><PERSON>, <PERSON><PERSON>, Page } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { formatEventDate, formatEventTime } from '../../utils/dateUtils';
import Loading from '../../components/utils/Loading';
import NewsComponent from '../../components/NewsComponent';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useApiCache from '../../hooks/useApiCache';
import RecentAnnouncements from '../../components/utils/RecentAnnouncements';
import AnnouncementDetail from '../../components/AnnouncementDetail';
import { ICONS } from '../../constants/icons';

const StudentEdu = () => {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const { user, classId, loading: authLoading } = useContext(AuthContext);
    const [eventPage, setEventPage] = useState(1);
    const [recentAnnouncements, setRecentAnnouncements] = useState([]);
    const observerRef = useRef(null);
    const eventsContainerRef = useRef(null);
    const hasLoadedInitialEvents = useRef(false);
    const isLoadingMore = useRef(false);
    const loadedEventIds = useRef(new Set()); // Theo dõi các event đã tải để tránh trùng lặp

    // Announcement modal states
    const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
    const [announcementDetailVisible, setAnnouncementDetailVisible] = useState(false);

    // Leave Request Management Modal
    const [leaveManagementVisible, setLeaveManagementVisible] = useState(false);

    // No Class Info Modal
    const [noClassModalVisible, setNoClassModalVisible] = useState(false);

    // Kiểm tra xác thực
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && !user.role.includes('student')) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Kiểm tra và hiển thị modal cảnh báo không có thông tin lớp học
    useEffect(() => {
        const showModal = localStorage.getItem('showNoClassModal');
        if (showModal === 'true') {
            setNoClassModalVisible(true);
            localStorage.removeItem('showNoClassModal'); // Remove flag after showing
        }
    }, []);

    // Kiểm tra thông tin lớp học của user
    useEffect(() => {
        if (user && user.role.includes('student') && !user.class && !authLoading) {
            setNoClassModalVisible(true);
        }
    }, [user, authLoading]);

    // API functions for useApiCache
    const fetchRecentAnnouncements = useCallback(async () => {
        if (!user) return [];
        const response = await authApi.get('/announcements/recent?limit=5');
        return Array.isArray(response.data) ? response.data : [];
    }, [user]);

    const fetchUnreadCount = useCallback(async () => {
        if (!user) return 0;
        const response = await authApi.get('/announcements/unread?limit=10');
        return response.data.success ? (response.data.data.unreadCount || 0) : 0;
    }, [user]);

    // Use useApiCache for announcements
    const {
        data: announcementsData = [],
        loading: announcementsLoading,
        refetch: refetchAnnouncements
    } = useApiCache(fetchRecentAnnouncements, [user?._id], {
        cacheKey: `student_announcements_${user?._id}`,
        enabled: !!user,
        cacheTime: 2 * 60 * 1000,
        onSuccess: (data) => {
            setRecentAnnouncements(data); // Đảm bảo state được update
        }
    });

    const {
        data: unreadCount = 0,
        refetch: refetchUnreadCount
    } = useApiCache(fetchUnreadCount, [user?._id], {
        cacheKey: `student_unread_count_${user?._id}`,
        enabled: !!user,
        cacheTime: 1 * 60 * 1000, // Cache 1 phút
    });

    // Combined refresh function
    const refreshAnnouncementData = useCallback(() => {
        refetchAnnouncements();
        refetchUnreadCount();
    }, [refetchAnnouncements, refetchUnreadCount]);

    // Handle announcement click
    const handleAnnouncementClick = useCallback(async (announcement) => {
        // Show detail modal first
        try {
            const response = await authApi.get(`/announcements/${announcement.id}`);
            setSelectedAnnouncement(response.data);
            setAnnouncementDetailVisible(true);
        } catch (err) {
            console.error('Error fetching announcement detail:', err);
            setSelectedAnnouncement(announcement);
            setAnnouncementDetailVisible(true);
        }

        // Nếu chưa đọc, đánh dấu đã đọc
        if (!announcement.isRead) {
            try {
                // Call API để đánh dấu đã đọc
                await authApi.post(`/announcements/${announcement.id}/read`);
                
                // Update UI sau khi API thành công
                setRecentAnnouncements(prev => 
                    prev.map(item => 
                        item.id === announcement.id 
                            ? { ...item, isRead: true }
                            : item
                    )
                );

                // Force refresh unread count
                await refetchUnreadCount(true); // Force refresh
                
            } catch (err) {
                console.error('Error marking announcement as read:', err);
            }
        }
    }, [refetchUnreadCount]);

    // Thêm useMemo để tạo key cho RecentAnnouncements
    const announcementKey = useMemo(() => {
        return `announcements-${Date.now()}-${recentAnnouncements.map(a => `${a.id}-${a.isRead}`).join(',')}-${unreadCount}`;
    }, [recentAnnouncements, unreadCount]);

    // API function for events with pagination
    const fetchEventsData = useCallback(async () => {
        if (!classId) return { events: [], totalPages: 1 };

        const response = await authApi.get(`/events/upcoming?classId=${classId}&page=1&limit=10`);

        // Xử lý phản hồi API
        if (response.data && Array.isArray(response.data.events)) {
            return {
                events: response.data.events,
                totalPages: response.data.totalPages || 1
            };
        } else if (Array.isArray(response.data)) {
            return {
                events: response.data,
                totalPages: 1
            };
        }
        return { events: [], totalPages: 1 };
    }, [classId]);

    // Use useApiCache for events
    const {
        data: eventsData = { events: [], totalPages: 1 },
        loading: eventsLoading,
        refetch: refetchEvents
    } = useApiCache(fetchEventsData, [classId], {
        cacheKey: `student_events_${classId}`,
        enabled: !!classId,
        cacheTime: 2 * 60 * 1000, // Cache 2 phút
    });

    const events = eventsData?.events;
    const totalEventPages = eventsData?.totalPages;

    // Tải dữ liệu ban đầu
    useEffect(() => {
        if (classId && !hasLoadedInitialEvents.current) {
            refetchEvents();
        }
    }, [classId, refetchEvents]);

    // Xử lý khi eventPage thay đổi
    useEffect(() => {
        if (hasLoadedInitialEvents.current && eventPage > 1 && !isLoadingMore.current) {
            refetchEvents();
        }
    }, [eventPage, refetchEvents]);

    // Load thêm sự kiện khi click nút "Xem thêm"
    const handleLoadMore = () => {
        if (eventPage < totalEventPages && !eventsLoading && !isLoadingMore.current) {
            setEventPage(prev => prev + 1);
        }
    };

    // Thiết lập IntersectionObserver
    useEffect(() => {
        if (!observerRef.current || isLoadingMore.current || eventPage >= totalEventPages) return;

        const observer = new IntersectionObserver(
            (entries) => {
                const entry = entries[0];

                if (entry.isIntersecting && !isLoadingMore.current && eventPage < totalEventPages) {
                    setEventPage(prev => prev + 1);
                }
            },
            { threshold: 0.5 }
        );

        const currentRef = observerRef.current;
        if (currentRef) {
            observer.observe(currentRef);
        }

        return () => {
            if (currentRef) {
                observer.unobserve(currentRef);
            }
            observer.disconnect();
        };
    }, [events?.length, eventPage, totalEventPages]);

    // Xử lý scroll ngang với throttle
    const handleScroll = useCallback(() => {
        if (isLoadingMore.current || eventPage >= totalEventPages) return;

        const container = eventsContainerRef.current;
        if (!container) return;

        const scrollPosition = container.scrollLeft;
        const containerWidth = container.clientWidth;
        const scrollWidth = container.scrollWidth;

        const isNearEnd = scrollWidth - (scrollPosition + containerWidth) < 100;

        if (isNearEnd && !isLoadingMore.current && eventPage < totalEventPages) {
            setEventPage(prev => prev + 1);
        }
    }, [eventPage, totalEventPages]);

    // Gắn sự kiện scroll với throttle
    useEffect(() => {
        const container = eventsContainerRef.current;
        if (!container || eventPage >= totalEventPages) return;

        let isThrottled = false;
        const throttledScroll = () => {
            if (!isThrottled) {
                handleScroll();
                isThrottled = true;
                setTimeout(() => {
                    isThrottled = false;
                }, 300);
            }
        };

        container.addEventListener('scroll', throttledScroll);
        return () => container.removeEventListener('scroll', throttledScroll);
    }, [handleScroll, eventPage, totalEventPages]);

    // Trạng thái hiển thị
    const showEventsLoading = eventsLoading && events?.length === 0;
    const showNoEvents = !eventsLoading && events?.length === 0 && hasLoadedInitialEvents.current;
    const showLoadMore = !eventsLoading && events?.length > 0 && eventPage < totalEventPages;

    const quickActions = [
        { "icon": ICONS.SUCCESS, "text": "Điểm danh", "path": "/attendance" },
        { "icon": ICONS.EDIT, "text": "Bài tập", "path": "/exams" },
        { "icon": ICONS.STATS, "text": "Điểm số", "path": "/grades" },
        { "icon": ICONS.CALENDAR, "text": "Lịch học", "path": "/schedule" }
    ];

    return (
        <Box
            className="container"
            style={{
                minHeight: '100vh',
                display: 'flex',
                flexDirection: 'column',
                backgroundColor: '#f5f5f5',
                position: 'relative',
                overscrollBehavior: 'none',
                WebkitOverflowScrolling: 'touch',
                touchAction: 'pan-y',
            }}
        >
            <Box
                style={{
                    position: 'sticky',
                    top: 0,
                    left: 0,
                    right: 0,
                    zIndex: 100,
                    backgroundColor: '#fff'
                }}
            >
                <HeaderEdu />
                <HeaderSpacer />
            </Box>

            <Box
                style={{
                    flex: 1,
                    paddingBottom: '60px',
                    overscrollBehavior: 'none',
                }}
            >
                <Box
                    className="quick-access"
                    style={{
                        padding: '15px',
                        display: 'grid',
                        gridTemplateColumns: 'repeat(4, 1fr)',
                        gap: '15px',
                        backgroundColor: 'white',
                    }}
                >
                    {quickActions.map((item, index) => (
                        <Box
                            key={index}
                            className="quick-item"
                            flex
                            flexDirection="column"
                            alignItems="center"
                            style={{ textAlign: 'center', cursor: 'pointer' }}
                            onClick={item.action || (() => navigate(item.path))}
                        >
                            <Box
                                className="quick-icon"
                                style={{
                                    width: '50px',
                                    height: '50px',
                                    borderRadius: '12px',
                                    backgroundColor: '#e8f0fe',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginBottom: '8px',
                                    color: '#0068ff',
                                    fontSize: '22px',
                                }}
                            >
                                {item.icon}
                            </Box>
                            <Text className="quick-text" style={{ fontSize: '12px', color: '#666' }}>
                                {item.text}
                            </Text>
                        </Box>
                    ))}
                </Box>

                <RecentAnnouncements
                    key={announcementKey}
                    recentAnnouncements={recentAnnouncements}
                    unreadCount={unreadCount}
                    announcementsLoading={announcementsLoading}
                    onAnnouncementClick={handleAnnouncementClick}
                    onViewAll={() => navigate('/announcements', {
                        state: {
                            returnToHome: '/student',
                            homePageName: 'Trang chủ học sinh'
                        }
                    })}
                />

                <Box
                    className="section"
                    style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}
                >
                    <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                        <Text className="section-title" bold size="large">
                            Sự kiện sắp tới
                        </Text>
                        <Box flex>
                            <Text
                                className="see-all"
                                style={{ color: '#0068ff', fontSize: '14px', cursor: 'pointer' }}
                                onClick={() => navigate('/all-events')}
                            >
                                Lịch đầy đủ
                            </Text>
                        </Box>
                    </Box>

                    {/* Hiển thị loading khi đang tải dữ liệu ban đầu */}
                    {showEventsLoading ? (
                        <Box style={{ padding: '20px 0', textAlign: 'center' }}>
                            <Loading />
                        </Box>
                    ) : (
                        <Box style={{ position: 'relative' }}>
                            <Box
                                className="upcoming-events"
                                ref={eventsContainerRef}
                                style={{
                                    display: 'flex',
                                    overflowX: 'auto',
                                    gap: '15px',
                                    paddingBottom: '15px',
                                    flexWrap: 'nowrap',
                                    scrollBehavior: 'smooth',
                                    WebkitOverflowScrolling: 'touch',
                                }}
                            >
                                {events?.map((item, index) => (
                                    <Box
                                        key={item._id || `event-${index}`}
                                        className="event-card"
                                        style={{
                                            minWidth: '250px',
                                            backgroundColor: '#f0f6ff',
                                            borderRadius: '8px',
                                            padding: '12px',
                                            borderLeft: '4px solid #0068ff',
                                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                            flexShrink: 0,
                                        }}
                                    >
                                        <Text className="event-date" bold style={{ color: '#0068ff', marginBottom: '5px' }}>
                                            {formatEventDate(item.startTime)}
                                        </Text>
                                        <Text className="event-title" bold style={{ marginBottom: '5px' }}>
                                            {item.title}
                                        </Text>
                                        <Text className="event-time" style={{ fontSize: '12px', color: '#666' }}>
                                            {formatEventTime(item.startTime, item.endTime, item.description)}
                                        </Text>

                                        {/* Gắn observer vào phần tử cuối cùng */}
                                        {index === events?.length - 1 && eventPage < totalEventPages && (
                                            <div ref={observerRef} style={{ height: '1px', width: '100%' }} />
                                        )}
                                    </Box>
                                ))}

                                {/* Loading indicator khi đang tải thêm */}
                                {eventsLoading && events?.length > 0 && (
                                    <Box
                                        style={{
                                            minWidth: '120px',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            flexShrink: 0,
                                            padding: '12px',
                                            borderRadius: '8px',
                                            backgroundColor: '#f9f9f9',
                                        }}
                                    >
                                        <LoadingIndicator />
                                    </Box>
                                )}

                                {/* Nút "Xem thêm" */}
                                {showLoadMore && (
                                    <Box
                                        style={{
                                            minWidth: '120px',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            flexShrink: 0,
                                            padding: '12px',
                                            borderRadius: '8px',
                                            backgroundColor: '#f0f6ff',
                                            cursor: 'pointer',
                                        }}
                                        onClick={handleLoadMore}
                                    >
                                        <Text style={{ color: '#0068ff' }}>Xem thêm</Text>
                                    </Box>
                                )}

                                {/* Empty state */}
                                {showNoEvents && (
                                    <Box style={{ padding: '20px 0', minWidth: '100%', textAlign: 'center' }}>
                                        <Text>Chưa có sự kiện</Text>
                                    </Box>
                                )}
                            </Box>

                            {/* Nút mũi tên scroll */}
                            {events?.length > 2 && (
                                <Box
                                    style={{
                                        position: 'absolute',
                                        right: 0,
                                        top: '40%',
                                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                        borderRadius: '50%',
                                        width: '36px',
                                        height: '36px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                        cursor: 'pointer',
                                        zIndex: 1,
                                    }}
                                    onClick={() => {
                                        if (eventsContainerRef.current) {
                                            eventsContainerRef.current.scrollLeft += 300;
                                        }
                                    }}
                                >
                                    <Text style={{ fontSize: '20px' }}>→</Text>
                                </Box>
                            )}
                        </Box>
                    )}
                </Box>

                <NewsComponent />
            </Box>

            {/* Announcement Detail Modal */}
            <AnnouncementDetail
                visible={announcementDetailVisible}
                onClose={() => {
                    setAnnouncementDetailVisible(false);
                    setSelectedAnnouncement(null);
                }}
                announcement={selectedAnnouncement}
                onBookmark={() => { }}
                isBookmarked={false}
                onViewReadStatus={() => { }}
                mode="received"
            />

            {/* No Class Info Modal */}
            <Modal
                visible={noClassModalVisible}
                title="Cảnh báo"
                onClose={() => setNoClassModalVisible(false)}
                actions={[
                    {
                        text: 'Đóng',
                        close: true,
                        onClick: () => setNoClassModalVisible(false),
                        style: {
                            backgroundColor: '#0068ff',
                            color: 'white'
                        }
                    }
                ]}
            >
                <Box style={{ padding: '20px', textAlign: 'center' }}>
                    <Text style={{
                        fontSize: '48px',
                        marginBottom: '20px',
                        display: 'block'
                    }}>
                        ⚠️
                    </Text>
                    <Text style={{
                        lineHeight: 1.5,
                        fontSize: '16px',
                        color: '#333'
                    }}>
                        Không tìm thấy thông tin lớp học cho năm học đã chọn. Vui lòng liên hệ với giáo viên hoặc ban giám hiệu để được hỗ trợ.
                    </Text>
                </Box>
            </Modal>

            {/* Leave Management Modal */}
            <Modal
                visible={leaveManagementVisible}
                title="Quản lý xin phép"
                onClose={() => setLeaveManagementVisible(false)}
            >
                <Box style={{ padding: '20px' }}>
                    <Text bold size="large" style={{ marginBottom: '20px', textAlign: 'center', color: '#0068ff' }}>
                        {ICONS.LIST} Hệ thống xin phép nghỉ học
                    </Text>

                    <Box style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                        {/* Tạo đơn xin nghỉ */}
                        <Box
                            onClick={() => {
                                setLeaveManagementVisible(false);
                                navigate('/student-leave-request');
                            }}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                padding: '16px',
                                backgroundColor: '#f0f8ff',
                                borderRadius: '12px',
                                cursor: 'pointer',
                                border: '2px solid #e0e8f0',
                                transition: 'all 0.2s'
                            }}
                        >
                            <Box style={{
                                width: '50px',
                                height: '50px',
                                borderRadius: '12px',
                                backgroundColor: '#0068ff',
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '24px',
                                marginRight: '15px'
                            }}>
                                {ICONS.HOSPITAL}
                            </Box>
                            <Box style={{ flex: 1 }}>
                                <Text bold style={{ fontSize: '16px', color: '#333', marginBottom: '4px' }}>
                                    Tạo đơn xin nghỉ học
                                </Text>
                                <Text style={{ fontSize: '13px', color: '#666' }}>
                                    Tạo đơn xin nghỉ vì ốm, việc gia đình...
                                </Text>
                            </Box>
                            <Text style={{ fontSize: '20px', color: '#0068ff' }}>→</Text>
                        </Box>

                        {/* Đơn xin nghỉ của tôi */}
                        <Box
                            onClick={() => {
                                setLeaveManagementVisible(false);
                                navigate('/my-leave-requests');
                            }}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                padding: '16px',
                                backgroundColor: '#f0f8ff',
                                borderRadius: '12px',
                                cursor: 'pointer',
                                border: '2px solid #e0e8f0',
                                transition: 'all 0.2s'
                            }}
                        >
                            <Box style={{
                                width: '50px',
                                height: '50px',
                                borderRadius: '12px',
                                backgroundColor: '#ff9500',
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '24px',
                                marginRight: '15px'
                            }}>
                                {ICONS.LIST}
                            </Box>
                            <Box style={{ flex: 1 }}>
                                <Text bold style={{ fontSize: '16px', color: '#333', marginBottom: '4px' }}>
                                    Đơn xin nghỉ của tôi
                                </Text>
                                <Text style={{ fontSize: '13px', color: '#666' }}>
                                    Xem lịch sử và trạng thái đơn xin nghỉ
                                </Text>
                            </Box>
                            <Text style={{ fontSize: '20px', color: '#ff9500' }}>→</Text>
                        </Box>
                    </Box>

                    <Box style={{ marginTop: '20px', padding: '12px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                        <Text style={{ fontSize: '12px', color: '#666', textAlign: 'center' }}>
                            {ICONS.INFO} Tip: Đơn xin nghỉ cần được giáo viên chủ nhiệm phê duyệt
                        </Text>
                    </Box>
                </Box>
            </Modal>

            <Box
                style={{
                    position: 'fixed',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: 100,
                    backgroundColor: '#fff',
                    borderTop: '1px solid #eee'
                }}
            >
                <BottomNavigationEdu />
            </Box>
        </Box>
    );
};

export default StudentEdu;