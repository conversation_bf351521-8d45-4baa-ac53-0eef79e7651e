import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, List, useNavigate, Modal, Button, Select } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import Loading from '../../components/utils/Loading';
import useNotification from '../../hooks/useNotification';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { formatDateTime } from '../../utils/dateUtils';

const ViolationsList = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const { success, error } = useNotification();
    const [violations, setViolations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedViolation, setSelectedViolation] = useState(null);
    const [detailVisible, setDetailVisible] = useState(false);
    const [appealProcessVisible, setAppealProcessVisible] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [filters, setFilters] = useState({
        classId: '',
        violationType: '',
        status: ''
    });
    const [classes, setClasses] = useState([]);
    const [violationTypes, setViolationTypes] = useState([]);

    // Fetch initial data
    useEffect(() => {
        fetchInitialData();
    }, []);

    // Fetch violations when filters change
    useEffect(() => {
        fetchViolations(1, false);
    }, [filters]);

    const fetchInitialData = async () => {
        try {
            const [classesRes, configRes] = await Promise.all([
                authApi.get('/directory/teacher/classes'),
                authApi.get('/violations/config')
            ]);
            
            // Ensure classes is always an array
            const classesData = Array.isArray(classesRes.data?.data) ? classesRes.data.data : 
                              Array.isArray(classesRes.data) ? classesRes.data : [];
            
            // Ensure violationTypes is always an array  
            const violationTypesData = Array.isArray(configRes.data?.data?.violationTypes) ? configRes.data.data.violationTypes :
                                     Array.isArray(configRes.data?.violationTypes) ? configRes.data.violationTypes : [];
            
            setClasses(classesData);
            setViolationTypes(violationTypesData);
        } catch (error) {
            console.error('Error fetching initial data:', error);
            // Ensure arrays are set even on error
            setClasses([]);
            setViolationTypes([]);
        }
    };

    // Fetch violations
    const fetchViolations = async (pageNum = 1, append = false) => {
        try {
            if (!append) setLoading(true);
            
            const params = new URLSearchParams({
                page: pageNum.toString(),
                limit: '10'
            });

            // Add filters
            if (filters.classId) params.append('classId', filters.classId);
            if (filters.violationType) params.append('violationType', filters.violationType);
            if (filters.status) params.append('status', filters.status);

            const response = await authApi.get(`/violations?${params.toString()}`);
            // Handle different response structures
            const newViolations = response.data?.data?.docs || response.data?.violations || response.data?.data || [];
            
            if (append) {
                setViolations(prev => [...prev, ...newViolations]);
            } else {
                setViolations(newViolations);
            }
            
            // Handle different pagination structures
            const pagination = response.data?.data || response.data?.pagination || {};
            setHasMore(pagination.hasNextPage || pagination.hasNext || false);
            setPage(pageNum);
        } catch (error) {
            console.error('Error fetching violations:', error);
        } finally {
            setLoading(false);
        }
    };

    // Get violation type label
    const getViolationTypeLabel = (type) => {
        const violationType = violationTypes.find(vt => vt.code === type);
        return violationType ? violationType.label : type;
    };

    // Get status label and color
    const getStatusInfo = (status) => {
        const statusMap = {
            'pending': { label: 'Chờ xử lý', color: '#f39c12', bg: '#fff3cd' },
            'processed': { label: 'Đã xử lý', color: '#28a745', bg: '#d4edda' },
            'appealed': { label: 'Đã khiếu nại', color: '#007bff', bg: '#d1ecf1' },
            'appeal_approved': { label: 'Khiếu nại được chấp nhận', color: '#28a745', bg: '#d4edda' },
            'appeal_rejected': { label: 'Khiếu nại bị từ chối', color: '#dc3545', bg: '#f8d7da' }
        };
        return statusMap[status] || { label: status, color: '#6c757d', bg: '#f8f9fa' };
    };

    // Handle violation detail
    const handleViolationDetail = async (violation) => {
        try {
            const response = await authApi.get(`/violations/${violation._id}`);
            console.log('Violation detail response:', response.data);
            // Handle the correct data structure
            const violationData = response.data.data || response.data;
            console.log('Setting violation data:', violationData);
            setSelectedViolation(violationData);
            setDetailVisible(true);
        } catch (err) {
            console.error('Error fetching violation detail:', err);
            error('Không thể tải chi tiết vi phạm');
        }
    };

    // Handle appeal processing
    const handleAppealProcess = async (result) => {
        try {
            await authApi.post(`/violations/${selectedViolation._id}/process-appeal`, {
                result
            });
            
            setAppealProcessVisible(false);
            setDetailVisible(false);
            fetchViolations(1, false); // Refresh list
            success(`Khiếu nại đã được ${result === 'approved' ? 'chấp nhận' : 'từ chối'}`);
        } catch (err) {
            console.error('Error processing appeal:', err);
            error('Có lỗi xảy ra khi xử lý khiếu nại');
        }
    };

    // Load more violations
    const loadMore = () => {
        if (hasMore && !loading) {
            fetchViolations(page + 1, true);
        }
    };

    // Handle filter change
    const handleFilterChange = (field, value) => {
        setFilters(prev => ({ ...prev, [field]: value }));
        setPage(1);
    };

    if (loading && violations.length === 0) {
        return <Loading />;
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Danh sách vi phạm"
                showBackButton={true}
                onBackClick={() => navigate('/teacher')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                {/* Filters */}
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '15px',
                    marginBottom: '15px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                    <Text bold style={{ fontSize: '16px', marginBottom: '15px', color: '#333' }}>
                        {ICONS.FILTER} Bộ lọc
                    </Text>
                    
                    <Box style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '12px' }}>
                        <Select
                            placeholder="Tất cả lớp"
                            value={filters.classId}
                            onChange={(value) => handleFilterChange('classId', value)}
                        >
                            <Select.Option value="" title="Tất cả lớp" />
                            {Array.isArray(classes) && classes.map(cls => (
                                <Select.Option key={cls._id || cls.id} value={cls._id || cls.id} title={cls.name} />
                            ))}
                        </Select>

                        <Select
                            placeholder="Tất cả loại vi phạm"
                            value={filters.violationType}
                            onChange={(value) => handleFilterChange('violationType', value)}
                        >
                            <Select.Option value="" title="Tất cả loại vi phạm" />
                            {Array.isArray(violationTypes) && violationTypes.map(type => (
                                <Select.Option key={type.code} value={type.code} title={type.label} />
                            ))}
                        </Select>

                        <Select
                            placeholder="Tất cả trạng thái"
                            value={filters.status}
                            onChange={(value) => handleFilterChange('status', value)}
                        >
                            <Select.Option value="" title="Tất cả trạng thái" />
                            <Select.Option value="pending" title="Chờ xử lý" />
                            <Select.Option value="processed" title="Đã xử lý" />
                            <Select.Option value="appealed" title="Đã khiếu nại" />
                            <Select.Option value="appeal_approved" title="Khiếu nại được chấp nhận" />
                            <Select.Option value="appeal_rejected" title="Khiếu nại bị từ chối" />
                        </Select>
                    </Box>
                </Box>

                {/* Violations List */}
                <Box style={{ backgroundColor: 'white', borderRadius: '12px', padding: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold style={{ fontSize: '18px', color: '#333' }}>
                            {ICONS.REPORT} Danh sách vi phạm
                        </Text>
                        <Button
                            onClick={() => navigate('/create-violation')}
                            style={{
                                borderRadius: '28px',
                                backgroundColor: '#dc3545',
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '4px',
                                padding: '10px',
                                border: 'none',
                                cursor: 'pointer',
                                transition: 'transform 0.2s ease',
                            }}
                        >
                            <Text style={{
                                display: 'flex',
                                alignItems: 'center',
                                lineHeight: 1,
                                fontSize: '20px'
                            }}>
                                +
                            </Text>
                        </Button>
                    </Box>

                    {loading ? (
                        <Loading />
                    ) : violations.length > 0 ? (
                        <Box>
                            {Array.isArray(violations) && violations.map((violation, index) => {
                                const statusInfo = getStatusInfo(violation.status);
                                return (
                                    <Box
                                        key={violation._id || index}
                                        style={{
                                            padding: '15px',
                                            border: '1px solid #e0e0e0',
                                            borderRadius: '8px',
                                            marginBottom: '10px',
                                            backgroundColor: '#fff',
                                            cursor: 'pointer',
                                            transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                                        }}
                                        onClick={() => handleViolationDetail(violation)}
                                        onMouseEnter={(e) => {
                                            e.currentTarget.style.transform = 'scale(1.02)';
                                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.currentTarget.style.transform = 'scale(1)';
                                            e.currentTarget.style.boxShadow = 'none';
                                        }}
                                    >
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                            <Text bold style={{ fontSize: '16px', color: '#333' }}>
                                                {violation.student?.name} ({violation.student?.studentId})
                                            </Text>
                                            <Box style={{
                                                padding: '4px 8px',
                                                borderRadius: '12px',
                                                backgroundColor: statusInfo.bg,
                                                border: `1px solid ${statusInfo.color}`,
                                                marginLeft: '10px'
                                            }}>
                                                <Text style={{ fontSize: '11px', color: statusInfo.color, fontWeight: 'bold' }}>
                                                    {statusInfo.label}
                                                </Text>
                                            </Box>
                                        </Box>
                                        
                                        <Text style={{ fontSize: '14px', color: '#0068ff', marginBottom: '8px', fontWeight: '500' }}>
                                            {ICONS.WARNING} {getViolationTypeLabel(violation.violationType)}
                                        </Text>
                                        
                                        <Text style={{ fontSize: '14px', color: '#666', marginBottom: '10px', lineHeight: '1.4' }}>
                                            {violation.description}
                                        </Text>
                                        
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingTop: '8px', borderTop: '1px solid #f0f0f0' }}>
                                            <Text style={{ fontSize: '12px', color: '#999' }}>
                                                {ICONS.SCHOOL} {violation.class?.name} • {formatDistanceToNow(new Date(violation.violationDate), { 
                                                    addSuffix: true, 
                                                    locale: vi 
                                                })}
                                            </Text>
                                            <Text style={{ fontSize: '14px', fontWeight: 'bold', color: '#dc3545' }}>
                                                -{violation.pointsDeducted} điểm
                                            </Text>
                                        </Box>
                                    </Box>
                                );
                            })}

                            {hasMore && (
                                <Box style={{ display: 'flex', justifyContent: 'center', marginTop: '20px' }}>
                                    <Button 
                                        onClick={loadMore}
                                        loading={loading}
                                        style={{ 
                                            backgroundColor: '#0068ff', 
                                            color: 'white',
                                            borderRadius: '8px',
                                            padding: '12px 24px'
                                        }}
                                    >
                                        {ICONS.REFRESH} Tải thêm
                                    </Button>
                                </Box>
                            )}
                        </Box>
                    ) : (
                        <Box style={{ textAlign: 'center', padding: '40px 20px' }}>
                            <Text style={{ fontSize: '48px', marginBottom: '15px' }}>{ICONS.INFO}</Text>
                            <Text bold style={{ fontSize: '18px', marginBottom: '10px', color: '#333' }}>
                                Không có vi phạm nào
                            </Text>
                            <Text style={{ color: '#666', fontSize: '14px' }}>
                                Chưa có vi phạm nào phù hợp với bộ lọc đã chọn.
                            </Text>
                        </Box>
                    )}
                </Box>
            </Box>

            {/* Violation Detail Modal */}
            <Modal
                visible={detailVisible}
                title="Chi tiết vi phạm"
                onClose={() => {
                    setDetailVisible(false);
                    setSelectedViolation(null);
                }}
            >
                {selectedViolation && selectedViolation._id && (
                    <Box style={{ padding: '20px' }}>
                        <Box style={{ marginBottom: '20px' }}>
                            <Text bold style={{ fontSize: '18px', color: '#0068ff', marginBottom: '10px' }}>
                                {selectedViolation.student?.name} ({selectedViolation.student?.studentId})
                            </Text>
                            
                            <Text bold style={{ fontSize: '16px', color: '#dc3545', marginBottom: '10px' }}>
                                {getViolationTypeLabel(selectedViolation.violationType)}
                            </Text>
                            
                            <Box style={{ marginBottom: '15px' }}>
                                <Text bold style={{ fontSize: '14px', marginBottom: '5px' }}>Mô tả:</Text>
                                <Text style={{ fontSize: '14px', color: '#333' }}>
                                    {selectedViolation.description}
                                </Text>
                            </Box>

                            <Box style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Lớp học:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.class?.name}
                                    </Text>
                                </Box>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Ngày vi phạm:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.violationDate ? 
                                            formatDateTime(selectedViolation.violationDate) : 
                                            'Không có thông tin'
                                        }
                                    </Text>
                                </Box>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Điểm trừ:
                                    </Text>
                                    <Text style={{ fontSize: '14px', color: '#dc3545', fontWeight: 'bold' }}>
                                        -{selectedViolation.pointsDeducted} điểm
                                    </Text>
                                </Box>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Địa điểm:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.location || 'Không có'}
                                    </Text>
                                </Box>
                            </Box>

                            {selectedViolation.appealReason && (
                                <Box style={{ marginBottom: '15px', padding: '12px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Lý do khiếu nại:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.appealReason}
                                    </Text>
                                </Box>
                            )}
                        </Box>

                        {selectedViolation.status === 'appealed' && (user?.role?.includes('admin') || user?.role?.includes('teacher')) && (
                            <Button
                                fullWidth
                                style={{ backgroundColor: '#007bff', color: 'white', marginTop: '10px' }}
                                onClick={() => {
                                    setDetailVisible(false);
                                    setAppealProcessVisible(true);
                                }}
                            >
                                {ICONS.DISCIPLINE} Xử lý khiếu nại
                            </Button>
                        )}
                    </Box>
                )}
            </Modal>

            {/* Appeal Process Modal */}
            <Modal
                visible={appealProcessVisible}
                title="Xử lý khiếu nại"
                onClose={() => setAppealProcessVisible(false)}
            >
                <Box style={{ padding: '20px' }}>
                    <Text style={{ marginBottom: '20px', fontSize: '14px', color: '#666' }}>
                        Quyết định về khiếu nại vi phạm của học sinh:
                    </Text>
                    
                    <Box style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                        <Button
                            fullWidth
                            style={{ backgroundColor: '#28a745', color: 'white', height: '48px' }}
                            onClick={() => handleAppealProcess('approved')}
                        >
                            {ICONS.SUCCESS} Chấp nhận khiếu nại
                        </Button>
                        
                        <Button
                            fullWidth
                            style={{ backgroundColor: '#dc3545', color: 'white', height: '48px' }}
                            onClick={() => handleAppealProcess('rejected')}
                        >
                            {ICONS.CROSS} Từ chối khiếu nại
                        </Button>
                    </Box>
                </Box>
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ViolationsList;
