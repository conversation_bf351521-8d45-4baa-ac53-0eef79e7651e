import React, { useState, useCallback, useContext, useMemo, useRef, useEffect } from 'react';
import { Box, Text, Button, Modal, useNavigate, Input, Select } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { format } from 'date-fns';
import vi from 'date-fns/locale/vi';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useApiCache from '../../hooks/useApiCache';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import DateInput from '../../components/utils/DateInput';
import { getLocation, getAccessToken } from "zmp-sdk/apis";
import LeaveRequestForm from '../../components/LeaveRequestForm';
import useNotification from '../../hooks/useNotification';
import DateRangeFilter from '../../components/utils/DateRangeFilter';
import { ICONS } from '@/constants/icons'; 

const Attendance = () => {
    const { user } = useContext(AuthContext);
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('attendance');
    const [modalVisible, setModalVisible] = useState(false);
    const [leaveModalVisible, setLeaveModalVisible] = useState(false);
    const [leaveRequests, setLeaveRequests] = useState([]);
    const [leaveLoading, setLeaveLoading] = useState(false);
    const [leaveFormData, setLeaveFormData] = useState({
        startDate: format(new Date(), 'yyyy-MM-dd'),
        endDate: format(new Date(), 'yyyy-MM-dd'),
        sessions: 'all-day',
        reason: '',
        attachments: []
    });
    const [confirmModal, setConfirmModal] = useState({
        visible: false,
        title: '',
        message: '',
        onConfirm: null,
        confirmText: '',
        cancelText: ''
    });

    // State để kiểm soát trạng thái loading khi điểm danh
    const [attendanceLoading, setAttendanceLoading] = useState(false);

    // State để track attendance records được thêm mới
    const [newAttendanceRecords, setNewAttendanceRecords] = useState([]);

    // State để lưu dữ liệu và trạng thái tải
    const [stats, setStats] = useState(null);
    const [statsLoading, setStatsLoading] = useState(false);
    const [error, setError] = useState('');
    const isMounted = useRef(false);

    // State cho filter leave requests
    const [leaveFilter, setLeaveFilter] = useState({
        status: 'all', // 'all', 'pending', 'approved', 'rejected'
        startDate: '',
        endDate: ''
    });

    // State cho filter stats
    const [statsFilter, setStatsFilter] = useState({
        period: 'semester', // 'semester', 'custom'
        startDate: '',
        endDate: ''
    });

    // Thêm state tạm thời cho filter
    const [tempLeaveFilter, setTempLeaveFilter] = useState({
        status: 'all',
        startDate: '',
        endDate: ''
    });

    // Thêm state tạm thời cho stats filter
    const [tempStatsFilter, setTempStatsFilter] = useState({
        period: 'semester',
        startDate: '',
        endDate: ''
    });

    const notification = useNotification();

    // Lấy lịch sử điểm danh của học sinh
    const fetchAttendanceHistory = useCallback(async () => {
        if (!user) return [];
        try {
            const response = await authApi.get(`/attendance/student/${user._id}`);
            return response.data.data || [];
        } catch (err) {
            console.error('Error fetching attendance history:', err);
            throw new Error('Lỗi khi tải lịch sử điểm danh');
        }
    }, [user]);

    // State để lưu tháng và năm hiện tại cho lịch
    const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
    const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
    const [selectedSession, setSelectedSession] = useState('all'); // 'all', 'morning', 'afternoon'

    // Ref để lưu vị trí cuộn
    const scrollPositionRef = useRef(0);

    // Lưu vị trí cuộn trước khi cập nhật state
    const saveScrollPosition = () => {
        scrollPositionRef.current = window.scrollY;
    };

    // Khôi phục vị trí cuộn sau khi cập nhật state
    const restoreScrollPosition = () => {
        setTimeout(() => {
            window.scrollTo(0, scrollPositionRef.current);
        }, 100);
    };

    // Sử dụng useEffect để ngăn chặn cuộn trang khi đang tải
    useEffect(() => {
        const handleScroll = (e) => {
            if (statsLoading) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
            return true;
        };

        const preventDefault = (e) => {
            if (statsLoading) {
                e.preventDefault();
            }
        };

        if (statsLoading) {
            document.body.style.overflow = 'hidden';
            document.body.style.position = 'fixed';
            document.body.style.width = '100%';
            document.body.style.top = `-${scrollPositionRef.current}px`;
            window.addEventListener('scroll', handleScroll, { passive: false });
            window.addEventListener('touchmove', preventDefault, { passive: false });
        } else {
            const scrollY = document.body.style.top;
            document.body.style.overflow = '';
            document.body.style.position = '';
            document.body.style.width = '';
            document.body.style.top = '';

            if (scrollY) {
                window.scrollTo(0, parseInt(scrollY || '0') * -1);
            }
        }

        return () => {
            document.body.style.overflow = '';
            document.body.style.position = '';
            document.body.style.width = '';
            document.body.style.top = '';
            window.removeEventListener('scroll', handleScroll);
            window.removeEventListener('touchmove', preventDefault);
        };
    }, [statsLoading]);

    // Sử dụng useApiCache để lấy dữ liệu và cache
    const {
        data: history = [],
        refetch: refetchHistory
    } = useApiCache(fetchAttendanceHistory, [user], {
        cacheKey: `attendance_history_${user?._id}`,
        enabled: !!user,
        cacheTime: 5 * 60 * 1000, // Cache 5 phút
    });

    // Wrapper cho refetchStats để đảm bảo sử dụng giá trị state mới nhất
    const fetchAttendanceData = useCallback(async (sessionOverride) => {
        if (!user) return;

        setStatsLoading(true);
        setError('');
        try {
            // Tính toán ngày đầu tháng và cuối tháng để lọc
            const startOfMonth = new Date(currentYear, currentMonth, 1);
            const endOfMonth = new Date(currentYear, currentMonth + 1, 0);

            const startDate = format(startOfMonth, 'yyyy-MM-dd');
            const endDate = format(endOfMonth, 'yyyy-MM-dd');

            // Sử dụng giá trị được truyền vào nếu có, nếu không thì sử dụng giá trị từ state
            const sessionToUse = sessionOverride || selectedSession;

            // Tạo URL với tham số session
            let url = `/attendance/statistics/student/${user._id}?startDate=${startDate}&endDate=${endDate}`;
            if (sessionToUse !== 'all') {
                url += `&session=${sessionToUse}`;
            }

            // Gọi API trực tiếp
            const response = await authApi.get(url);

            if (response.data.success) {
                setStats(response.data.data);
                return response.data.data;
            } else {
                throw new Error(response.data.msg || 'Lỗi khi tải thống kê điểm danh');
            }
        } catch (err) {
            console.error('Error fetching attendance data:', err);
            setError('Lỗi khi tải dữ liệu điểm danh');
            throw err;
        } finally {
            setStatsLoading(false);
        }
    }, [user, currentMonth, currentYear, selectedSession]);

    // Hàm chung để xử lý thay đổi session
    const handleSessionChange = (newSession) => {
        if (selectedSession !== newSession) {
            setSelectedSession(newSession);
            fetchAttendanceData(newSession);
        }
    };

    // Tải dữ liệu ban đầu khi component mount
    useEffect(() => {
        if (user) {
            fetchAttendanceData();
        }
    }, [user, fetchAttendanceData]);

    // Xử lý chuyển đổi tháng
    const handleMonthChange = async (direction) => {
        let newMonth = currentMonth;
        let newYear = currentYear;

        if (direction === 'prev') {
            newMonth = currentMonth - 1;
            if (newMonth < 0) {
                newMonth = 11;
                newYear = currentYear - 1;
            }
        } else {
            newMonth = currentMonth + 1;
            if (newMonth > 11) {
                newMonth = 0;
                newYear = currentYear + 1;
            }
        }

        setCurrentMonth(newMonth);
        setCurrentYear(newYear);

        // Đợi một chút để state được cập nhật
        await new Promise(resolve => setTimeout(resolve, 50));
        fetchAttendanceData();
    };

    // Kiểm tra xem có điểm danh hôm nay không
    const todayAttendance = useMemo(() => {
        const today = new Date();

        // Kiểm tra trong newAttendanceRecords trước (dữ liệu mới nhất)
        const newTodayRecord = newAttendanceRecords.find(record => {
            const recordDate = new Date(record.date);
            return recordDate.getDate() === today.getDate() &&
                recordDate.getMonth() === today.getMonth() &&
                recordDate.getFullYear() === today.getFullYear();
        });

        if (newTodayRecord) {
            return newTodayRecord;
        }

        // Nếu không có trong newAttendanceRecords, kiểm tra trong history
        if (!history || history.length === 0) return null;

        return history.find(record => {
            const recordDate = new Date(record.date);
            return recordDate.getDate() === today.getDate() &&
                recordDate.getMonth() === today.getMonth() &&
                recordDate.getFullYear() === today.getFullYear();
        });
    }, [history, newAttendanceRecords]);

    // Không sử dụng biến loading nữa vì chúng ta đã xử lý loading cho từng tab

    // Định dạng ngày
    const formatDate = (dateString) => {
        try {
            return format(new Date(dateString), 'EEEE, dd/MM/yyyy', { locale: vi });
        } catch {
            return 'N/A';
        }
    };

    // Xử lý điểm danh
    const handleConfirmAttendance = async () => {
        try {
            // Bắt đầu loading
            setAttendanceLoading(true);

            // Lấy accessToken từ Zalo
            const accessToken = await new Promise((resolve, reject) => {
                getAccessToken({
                    success: (token) => {
                        console.log("accessToken:", token);
                        resolve(token);
                    },
                    fail: (error) => {
                        console.log("accessToken error:", error);
                        reject(error);
                    },
                });
            });

            // Lấy token vị trí từ Zalo
            const locationData = await new Promise((resolve, reject) => {
                getLocation({
                    success: (data) => {
                        console.log("Location data:", data);
                        resolve(data);
                    },
                    fail: (error) => {
                        console.log("Location error:", error);
                        reject(error);
                    },
                });
            });

            const { token: locationToken } = locationData;
            console.log("Location token:", locationToken);

            // Chuẩn bị dữ liệu điểm danh
            const attendanceData = {
                classId: user.class.id, // ID lớp học
                session: new Date().getHours() < 12 ? "morning" : "afternoon", // Buổi học dựa vào thời gian
                location: "", // Vị trí lớp học
                accessToken: accessToken, // Token xác thực từ Zalo
                token: locationToken // Token vị trí từ Zalo
            };

            // Gửi yêu cầu điểm danh
            const response = await authApi.post('/attendance', attendanceData);

            if (response.data.success) {
                console.log('Attendance successful, refreshing data...');

                // Thêm record mới vào state ngay lập tức để UI update ngay
                const newRecord = {
                    _id: response.data.data._id,
                    date: response.data.data.date,
                    session: response.data.data.session,
                    status: response.data.data.status,
                    location: response.data.data.location || 'Không có thông tin',
                    class: { name: user.class?.name || '10A1' },
                    createdAt: response.data.data.createdAt
                };

                setNewAttendanceRecords(prev => [newRecord, ...prev]);

                // Clear cache và force refresh dữ liệu ngay lập tức
                try {
                    // Xóa cache cũ trong localStorage (nếu useApiCache sử dụng localStorage)
                    const cacheKey = `attendance_history_${user?._id}`;
                    localStorage.removeItem(cacheKey);

                    // Force refresh cả hai API calls
                    await Promise.all([
                        refetchHistory(true), // Force refresh
                        fetchAttendanceData()
                    ]);

                    console.log('Data refreshed successfully');
                } catch (refreshError) {
                    console.error('Error refreshing data:', refreshError);
                }

                setModalVisible(true);
                notification.showSuccess(
                    'Điểm danh thành công',
                    `Thời gian: ${format(new Date(), 'HH:mm - dd/MM/yyyy')}\nTrạng thái: ${new Date().getHours() < 7 ? 'Đúng giờ' : 'Đi trễ'}`
                );
            }
        } catch (err) {
            console.error('Error confirming attendance:', err);
            notification.showError(
                'Lỗi',
                err.response?.data?.msg || 'Lỗi khi xác nhận điểm danh'
            );
        } finally {
            // Kết thúc loading dù thành công hay thất bại
            setAttendanceLoading(false);
        }
    };

    // Lấy danh sách yêu cầu xin phép
    const fetchLeaveRequests = useCallback(async () => {
        if (!user) return;

        try {
            setLeaveLoading(true);

            // Tạo URL cơ bản
            let url = `/leave-requests/student/${user._id}`;
            const queryParams = [];

            // Chỉ thêm ngày vào URL nếu người dùng đã chọn
            if (leaveFilter.startDate && leaveFilter.endDate) {
                queryParams.push(`startDate=${leaveFilter.startDate}`);
                queryParams.push(`endDate=${leaveFilter.endDate}`);
            }

            // Thêm filter status nếu không phải 'all'
            if (leaveFilter.status !== 'all') {
                queryParams.push(`status=${leaveFilter.status}`);
            }

            // Thêm các query params vào URL nếu có
            if (queryParams.length > 0) {
                url += '?' + queryParams.join('&');
            }

            const response = await authApi.get(url);

            if (response.data.success) {
                setLeaveRequests(response.data.data);
            }
        } catch (err) {
            console.error('Error fetching leave requests:', err);
            alert('Lỗi khi tải danh sách yêu cầu xin phép');
        } finally {
            setLeaveLoading(false);
        }
    }, [user, leaveFilter]);

    // Tải danh sách yêu cầu xin phép khi chuyển tab hoặc filter thay đổi
    useEffect(() => {
        if (activeTab === 'leave' && user) {
            // Chỉ fetch khi chuyển tab, không fetch khi filter thay đổi
            fetchLeaveRequests();
        }
    }, [activeTab, user]); // Bỏ fetchLeaveRequests khỏi dependencies

    // Hàm xử lý thay đổi filter cho leave requests
    const handleLeaveFilterChange = (field, value) => {
        const newFilter = {
            ...tempLeaveFilter,
            [field]: value
        };
        setTempLeaveFilter(newFilter);
        
        // Automatically apply filter when changing status
        if (field === 'status') {
            setLeaveFilter(newFilter);
            
            // Create URL with the new filter
            let url = `/leave-requests/student/${user._id}`;
            const queryParams = [];

            if (newFilter.startDate && newFilter.endDate) {
                queryParams.push(`startDate=${newFilter.startDate}`);
                queryParams.push(`endDate=${newFilter.endDate}`);
            }

            if (newFilter.status !== 'all') {
                queryParams.push(`status=${newFilter.status}`);
            }

            if (queryParams.length > 0) {
                url += '?' + queryParams.join('&');
            }

            // Call API with new URL
            setLeaveLoading(true);
            authApi.get(url)
                .then(response => {
                    if (response.data.success) {
                        setLeaveRequests(response.data.data);
                    }
                })
                .catch(err => {
                    console.error('Error fetching leave requests:', err);
                    alert('Lỗi khi tải danh sách yêu cầu xin phép');
                })
                .finally(() => {
                    setLeaveLoading(false);
                });
        }
    };

    // Thêm hàm xử lý áp dụng filter
    const handleApplyLeaveFilter = () => {
        // Sử dụng tempLeaveFilter trực tiếp thay vì đợi state update
        setLeaveFilter(tempLeaveFilter);
        
        // Tạo URL với giá trị mới nhất từ tempLeaveFilter
        let url = `/leave-requests/student/${user._id}`;
        const queryParams = [];

        if (tempLeaveFilter.startDate && tempLeaveFilter.endDate) {
            queryParams.push(`startDate=${tempLeaveFilter.startDate}`);
            queryParams.push(`endDate=${tempLeaveFilter.endDate}`);
        }

        if (tempLeaveFilter.status !== 'all') {
            queryParams.push(`status=${tempLeaveFilter.status}`);
        }

        if (queryParams.length > 0) {
            url += '?' + queryParams.join('&');
        }

        // Gọi API trực tiếp với URL mới
        setLeaveLoading(true);
        authApi.get(url)
            .then(response => {
                if (response.data.success) {
                    setLeaveRequests(response.data.data);
                }
            })
            .catch(err => {
                console.error('Error fetching leave requests:', err);
                alert('Lỗi khi tải danh sách yêu cầu xin phép');
            })
            .finally(() => {
                setLeaveLoading(false);
            });
    };

    // Cập nhật hàm clearLeaveFilters
    const clearLeaveFilters = () => {
        const clearedFilter = {
            status: 'all',
            startDate: '',
            endDate: ''
        };
        setTempLeaveFilter(clearedFilter);
        setLeaveFilter(clearedFilter);
        
        // Gọi API trực tiếp với URL không có filter
        setLeaveLoading(true);
        authApi.get(`/leave-requests/student/${user._id}`)
            .then(response => {
                if (response.data.success) {
                    setLeaveRequests(response.data.data);
                }
            })
            .catch(err => {
                console.error('Error fetching leave requests:', err);
                alert('Lỗi khi tải danh sách yêu cầu xin phép');
            })
            .finally(() => {
                setLeaveLoading(false);
            });
    };

    // Cập nhật useEffect để khởi tạo tempLeaveFilter khi leaveFilter thay đổi
    useEffect(() => {
        setTempLeaveFilter(leaveFilter);
    }, [leaveFilter]);

    // Xử lý xin nghỉ phép
    const handleLeaveRequest = () => {
        setLeaveModalVisible(true);
    };

    // Gửi yêu cầu xin phép
    const submitLeaveRequest = async (formData) => {
        try {
            setLeaveLoading(true);

            // Convert sessions array to string format
            const sessions = formData.sessions.length === 2 ? 'all-day' : formData.sessions[0];

            // Process attachments
            const processedAttachments = await Promise.all(
                formData.attachments.map(async (att) => {
                    return new Promise((resolve, reject) => {
                        const img = new Image();
                        img.onload = () => {
                            const reader = new FileReader();
                            reader.onloadend = () => {
                                resolve({
                                    data: reader.result.split(',')[1], // Remove data URL prefix
                                    width: img.width,
                                    height: img.height,
                                    contentType: att.file.type
                                });
                            };
                            reader.onerror = reject;
                            reader.readAsDataURL(att.file);
                        };
                        img.onerror = reject;
                        img.src = att.previewUrl;
                    });
                })
            );

            const requestData = {
                studentId: user._id,
                classId: user.class.id,
                startDate: formData.startDate,
                endDate: formData.endDate,
                sessions: sessions,
                reason: formData.reason,
                attachments: processedAttachments
            };

            const response = await authApi.post('/leave-requests', requestData);

            if (response.data.success) {
                setLeaveModalVisible(false);

                // Reset form data
                setLeaveFormData({
                    startDate: format(new Date(), 'yyyy-MM-dd'),
                    endDate: format(new Date(), 'yyyy-MM-dd'),
                    sessions: 'all-day',
                    reason: '',
                    attachments: []
                });

                notification.showSuccess(
                    'Thành công',
                    'Gửi yêu cầu xin phép thành công',
                    () => {
                        // Cập nhật lại danh sách yêu cầu
                        fetchLeaveRequests();
                    }
                );
            }
        } catch (err) {
            console.error('Error submitting leave request:', err);
            notification.showError(
                'Lỗi',
                err.response?.data?.msg || 'Lỗi khi gửi yêu cầu xin phép'
            );
        } finally {
            setLeaveLoading(false);
        }
    };

    // Hiển thị modal xác nhận
    const showConfirmModal = (title, message, onConfirm, confirmText = 'Xác nhận', cancelText = 'Hủy') => {
        setConfirmModal({
            visible: true,
            title,
            message,
            onConfirm,
            confirmText,
            cancelText
        });
    };

    // Đóng modal xác nhận
    const closeConfirmModal = () => {
        setConfirmModal(prev => ({
            ...prev,
            visible: false
        }));
    };

    // Xóa yêu cầu xin phép
    const deleteLeaveRequest = async (id) => {
        notification.confirmDelete(
            'Xóa yêu cầu xin phép',
            'Bạn có chắc chắn muốn xóa yêu cầu xin phép này không?',
            async () => {
                try {
                    setLeaveLoading(true);

                    const response = await authApi.delete(`/leave-requests/${id}`);

                    if (response.data.success) {
                        // Hiển thị modal thông báo thành công
                        showConfirmModal(
                            'Thành công',
                            'Xóa yêu cầu thành công',
                            () => {
                                closeConfirmModal();
                                // Cập nhật lại danh sách yêu cầu
                                fetchLeaveRequests();
                            },
                            'Đóng',
                            null
                        );
                        notification.showSuccess('Thành công', 'Đã xóa yêu cầu xin phép');
                    }
                } catch (err) {
                    console.error('Error deleting leave request:', err);
                    // Hiển thị modal thông báo lỗi
                    showConfirmModal(
                        'Lỗi',
                        err.response?.data?.msg || 'Lỗi khi xóa yêu cầu',
                        closeConfirmModal,
                        'Đóng',
                        null
                    );
                    notification.showError('Lỗi', 'Không thể xóa yêu cầu. Vui lòng thử lại sau.');
                } finally {
                    setLeaveLoading(false);
                }
            },
            'Xóa',
            'Hủy'
        );
    };

    // Không return Loading cho toàn bộ trang nữa

    // Hàm xử lý thay đổi stats filter
    const handleStatsFilterChange = (field, value) => {
        setTempStatsFilter(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Hàm xử lý áp dụng stats filter
    const handleApplyStatsFilter = () => {
        setStatsFilter(tempStatsFilter);
        
        // Tạo URL với giá trị mới nhất từ tempStatsFilter
        let url = `/attendance/statistics/student/${user._id}`;
        const queryParams = [];

        if (tempStatsFilter.period === 'custom' && tempStatsFilter.startDate && tempStatsFilter.endDate) {
            queryParams.push(`startDate=${tempStatsFilter.startDate}`);
            queryParams.push(`endDate=${tempStatsFilter.endDate}`);
        }

        if (queryParams.length > 0) {
            url += '?' + queryParams.join('&');
        }

        // Gọi API trực tiếp với URL mới
        setStatsLoading(true);
        authApi.get(url)
            .then(response => {
                if (response.data.success) {
                    setStats(response.data.data);
                }
            })
            .catch(err => {
                console.error('Error fetching stats:', err);
                setError('Lỗi khi tải dữ liệu thống kê');
            })
            .finally(() => {
                setStatsLoading(false);
            });
    };

    // Hàm xử lý xóa stats filter
    const clearStatsFilters = () => {
        const clearedFilter = {
            period: 'semester',
            startDate: '',
            endDate: ''
        };
        setTempStatsFilter(clearedFilter);
        setStatsFilter(clearedFilter);
        
        // Gọi API trực tiếp với URL không có filter
        setStatsLoading(true);
        authApi.get(`/attendance/statistics/student/${user._id}`)
            .then(response => {
                if (response.data.success) {
                    setStats(response.data.data);
                }
            })
            .catch(err => {
                console.error('Error fetching stats:', err);
                setError('Lỗi khi tải dữ liệu thống kê');
            })
            .finally(() => {
                setStatsLoading(false);
            });
    };

    // Cập nhật useEffect để khởi tạo tempStatsFilter khi statsFilter thay đổi
    useEffect(() => {
        setTempStatsFilter(statsFilter);
    }, [statsFilter]);

    return (
        <Box
            className="container"
            style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '80px' }}
        >
            <HeaderEdu title="Điểm Danh" showBackButton={true} />
            <HeaderSpacer />
            <Box className="page-container" style={{ padding: '15px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
                {/* Status Card */}
                <Box
                    className="status-card"
                    style={{
                        background: 'linear-gradient(135deg, #2196f3, #0068ff)',
                        color: 'white',
                        padding: '20px',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px rgba(0, 104, 255, 0.2)',
                    }}
                >
                    <Text className="status-title" style={{ fontSize: '16px', marginBottom: '15px', opacity: 0.9 }}>
                        Trạng thái hôm nay
                    </Text>
                    <Box className="status-info" style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                        <Text className="status-icon" style={{ fontSize: '36px', marginRight: '15px' }}>
                            {todayAttendance ? '✓' : '!'}
                        </Text>
                        <Box className="status-details">
                            <Text className="status-text" style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '5px' }}>
                                {todayAttendance
                                    ? `Điểm danh ${todayAttendance.status === 'present' ? 'có mặt' :
                                        todayAttendance.status === 'late' ? 'đi trễ' :
                                            todayAttendance.status === 'absent' ? 'vắng mặt' : todayAttendance.status}`
                                    : 'Chưa điểm danh'}
                            </Text>
                            <Text className="status-subtext" style={{ fontSize: '14px', opacity: 0.8 }}>
                                {todayAttendance
                                    ? `Hôm nay lúc ${format(new Date(todayAttendance.date), 'HH:mm')} • ${todayAttendance.session === 'morning' ? 'Buổi sáng' : 'Buổi chiều'}`
                                    : 'Vui lòng thực hiện điểm danh cho buổi học hôm nay'}
                            </Text>
                        </Box>
                    </Box>
                    <Box className="status-actions" style={{ display: 'flex', marginTop: '10px', gap: '10px' }}>
                        <Button
                            className="btn btn-outline"
                            style={{
                                backgroundColor: 'transparent',
                                border: '1px solid white',
                                color: 'white',
                                flex: 1,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '5px',
                                padding: '8px 12px',
                                minWidth: '100px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                            }}
                            onClick={() => setActiveTab('history')}
                        >
                            <span style={{ fontSize: '16px' }}>{ICONS.LIST}</span>
                            <span>Lịch sử</span>
                        </Button>
                        {todayAttendance ? (
                            <Button
                                className="btn"
                                style={{
                                    backgroundColor: 'white',
                                    color: '#0068ff',
                                    flex: 1,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    gap: '5px',
                                    padding: '8px 12px',
                                    minWidth: '100px',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis'
                                }}
                                onClick={handleLeaveRequest}
                            >
                                <span style={{ fontSize: '16px' }}>{ICONS.EDIT}</span>
                                <span>Xin phép nghỉ</span>
                            </Button>
                        ) : (
                            <Button
                                className="btn"
                                style={{
                                    backgroundColor: 'white',
                                    color: '#0068ff',
                                    flex: 1,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    gap: '5px',
                                    padding: '8px 12px',
                                    minWidth: '100px',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis'
                                }}
                                onClick={handleConfirmAttendance}
                                disabled={attendanceLoading}
                            >
                                <span style={{ fontSize: '16px' }}>{attendanceLoading ? ICONS.LOADING : ICONS.DRAFT}</span>
                                <span>{attendanceLoading ? 'Đang điểm danh' : 'Điểm danh ngay'}</span>
                            </Button>
                        )}
                    </Box>
                </Box>

                {/* Thống kê */}
                <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', margin: '15px 0 10px', color: '#333' }}>
                    Thống kê điểm danh tháng này
                </Text>
                <Box className="stats-container" style={{ display: 'flex', gap: '10px', marginBottom: '15px', overflowX: 'auto' }}>
                    <Box className="stat-card" style={{ flex: 1, backgroundColor: 'white', borderRadius: '8px', padding: '15px', textAlign: 'center', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                        <Text className="stat-value stat-present" style={{ fontSize: '20px', fontWeight: 'bold', color: '#4caf50' }}>
                            {stats?.statusCounts?.present || 0}
                        </Text>
                        <Text className="stat-label" style={{ fontSize: '12px', color: '#666' }}>
                            Có mặt
                        </Text>
                    </Box>
                    <Box className="stat-card" style={{ flex: 1, backgroundColor: 'white', borderRadius: '8px', padding: '15px', textAlign: 'center', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                        <Text className="stat-value stat-late" style={{ fontSize: '20px', fontWeight: 'bold', color: '#f9a825' }}>
                            {stats?.statusCounts?.late || 0}
                        </Text>
                        <Text className="stat-label" style={{ fontSize: '12px', color: '#666' }}>
                            Đi trễ
                        </Text>
                    </Box>
                    <Box className="stat-card" style={{ flex: 1, backgroundColor: 'white', borderRadius: '8px', padding: '15px', textAlign: 'center', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                        <Text className="stat-value stat-absent" style={{ fontSize: '20px', fontWeight: 'bold', color: '#f44336' }}>
                            {stats?.statusCounts?.absent || 0}
                        </Text>
                        <Text className="stat-label" style={{ fontSize: '12px', color: '#666' }}>
                            Vắng mặt
                        </Text>
                    </Box>
                    <Box className="stat-card" style={{ flex: 1, backgroundColor: 'white', borderRadius: '8px', padding: '15px', textAlign: 'center', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                        <Text className="stat-value stat-total" style={{ fontSize: '20px', fontWeight: 'bold', color: '#0068ff' }}>
                            {stats?.statusCounts?.total || 0}
                        </Text>
                        <Text className="stat-label" style={{ fontSize: '12px', color: '#666' }}>
                            Tổng số
                        </Text>
                    </Box>
                </Box>

                {/* Tabs Container */}
                <Box className="tabs-container-wrapper" style={{
                    position: 'relative',
                    backgroundColor: '#f0f0f0',
                    borderRadius: '8px',
                    padding: '2px',
                    marginBottom: '15px',
                    overflow: 'hidden'
                }}>
                    <Box className="tabs-container" style={{
                        display: 'flex',
                        overflowX: 'auto',
                        WebkitOverflowScrolling: 'touch', // Cho iOS
                        scrollbarWidth: 'none', // Ẩn scrollbar trên Firefox
                        msOverflowStyle: 'none', // Ẩn scrollbar trên IE/Edge
                        whiteSpace: 'nowrap'
                    }}>
                        {/* CSS để ẩn scrollbar trên Chrome/Safari */}
                        <style>
                            {`
                            .tabs-container::-webkit-scrollbar {
                                display: none;
                            }
                            `}
                        </style>
                        {['attendance', 'history', 'stats', 'leave'].map((tab) => (
                            <Button
                                key={tab}
                                className={`tab ${activeTab === tab ? 'active' : ''}`}
                                style={{
                                    minWidth: '90px', // Đảm bảo kích thước tối thiểu
                                    flex: '0 0 auto', // Không co giãn, giữ kích thước
                                    textAlign: 'center',
                                    padding: '10px',
                                    fontSize: '14px',
                                    fontWeight: 500,
                                    borderRadius: '6px',
                                    backgroundColor: activeTab === tab ? '#0068ff' : 'transparent',
                                    color: activeTab === tab ? 'white' : '#333',
                                    margin: '0 2px', // Thêm khoảng cách giữa các tab
                                }}
                                onClick={() => setActiveTab(tab)}
                            >
                                {tab === 'attendance' ? 'Điểm danh' :
                                    tab === 'history' ? 'Lịch sử ĐD' :
                                        tab === 'stats' ? 'Thống kê' : 'Xin phép'}
                            </Button>
                        ))}
                    </Box>
                </Box>

                {/* Tab Content - Điểm danh */}
                {activeTab === 'attendance' && (
                    <Box>
                        {todayAttendance ? (
                            <Box className="attendance-success" style={{
                                padding: '30px 20px',
                                backgroundColor: 'white',
                                borderRadius: '8px',
                                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                textAlign: 'center'
                            }}>
                                <Box style={{
                                    width: '80px',
                                    height: '80px',
                                    borderRadius: '50%',
                                    backgroundColor: '#e6f7e6',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    margin: '0 auto 20px auto',
                                    fontSize: '40px',
                                    color: '#4caf50'
                                }}>
                                    ✓
                                </Box>
                                <Text style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '10px' }}>
                                    Bạn đã điểm danh hôm nay
                                </Text>
                                <Text style={{ color: '#666', marginBottom: '5px' }}>
                                    Thời gian: {format(new Date(todayAttendance.date), 'HH:mm - dd/MM/yyyy')}
                                </Text>
                                <Text style={{
                                    color: todayAttendance.status === 'present' ? '#4caf50' :
                                        todayAttendance.status === 'late' ? '#f9a825' : '#f44336',
                                    fontWeight: 500
                                }}>
                                    Trạng thái: {
                                        todayAttendance.status === 'present' ? 'Có mặt' :
                                            todayAttendance.status === 'late' ? 'Đi trễ' :
                                                todayAttendance.status === 'absent' ? 'Vắng mặt' :
                                                    todayAttendance.status
                                    }
                                </Text>
                            </Box>
                        ) : (
                            <Box className="attendance-form" style={{
                                padding: '20px',
                                backgroundColor: 'white',
                                borderRadius: '8px',
                                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                            }}>
                                <Text style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '15px' }}>
                                    Điểm danh buổi học hôm nay
                                </Text>

                                <Box style={{
                                    padding: '15px',
                                    backgroundColor: '#f5f5f5',
                                    borderRadius: '8px',
                                    marginBottom: '20px'
                                }}>
                                    <Text style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>
                                        Thông tin điểm danh:
                                    </Text>
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                        <Text style={{ color: '#666' }}>Ngày:</Text>
                                        <Text style={{ fontWeight: 500 }}>{format(new Date(), 'dd/MM/yyyy')}</Text>
                                    </Box>
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                        <Text style={{ color: '#666' }}>Buổi học:</Text>
                                        <Text style={{ fontWeight: 500 }}>{new Date().getHours() < 12 ? 'Sáng' : 'Chiều'}</Text>
                                    </Box>
                                    <Box style={{ display: 'flex', justifyContent: 'space-between' }}>
                                        <Text style={{ color: '#666' }}>Lớp:</Text>
                                        <Text style={{ fontWeight: 500 }}>10A1</Text>
                                    </Box>
                                </Box>

                                <Button
                                    style={{
                                        backgroundColor: '#4caf50',
                                        color: 'white',
                                        width: '100%',
                                        height: '48px',
                                        fontSize: '16px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        gap: '8px',
                                        padding: '0 16px',
                                        whiteSpace: 'nowrap',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis'
                                    }}
                                    onClick={handleConfirmAttendance}
                                    disabled={attendanceLoading}
                                >
                                    <span style={{ fontSize: '20px', flexShrink: 0 }}>{attendanceLoading ? '⏳' : '✓'}</span>
                                    <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>{attendanceLoading ? 'Đang điểm danh' : 'Xác nhận điểm danh'}</span>
                                </Button>

                                <Text style={{ fontSize: '13px', color: '#666', marginTop: '15px', textAlign: 'center' }}>
                                    Lưu ý: Điểm danh sẽ được ghi nhận dựa trên thời gian hiện tại
                                </Text>
                            </Box>
                        )}
                    </Box>
                )}

                {/* Tab Content - Lịch sử */}
                {activeTab === 'history' && (
                    <Box>
                        {statsLoading ? (
                            <LoadingIndicator />
                        ) : (
                            <Box className="calendar-view" style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', marginBottom: '15px' }}>
                                <Box className="calendar-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                                    <Text className="calendar-title" style={{ fontSize: '16px', fontWeight: 'bold' }}>
                                        Tháng {currentMonth + 1}, {currentYear}
                                    </Text>
                                    <Box className="calendar-nav" style={{ display: 'flex', gap: '15px' }}>
                                        <Box
                                            className="calendar-nav-btn"
                                            style={{
                                                background: 'none',
                                                fontSize: '18px',
                                                color: '#0068ff',
                                                cursor: 'pointer',
                                                width: '30px',
                                                height: '30px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                userSelect: 'none'
                                            }}
                                            onClick={() => handleMonthChange('prev')}
                                        >
                                            ←
                                        </Box>
                                        <Box
                                            className="calendar-nav-btn"
                                            style={{
                                                background: 'none',
                                                fontSize: '18px',
                                                color: '#0068ff',
                                                cursor: 'pointer',
                                                width: '30px',
                                                height: '30px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                userSelect: 'none'
                                            }}
                                            onClick={() => handleMonthChange('next')}
                                        >
                                            →
                                        </Box>
                                    </Box>
                                </Box>

                                {/* Bộ lọc buổi học */}
                                <div className="session-filter" style={{ display: 'flex', gap: '10px', marginBottom: '15px' }}>
                                    <div
                                        style={{
                                            padding: '5px 10px',
                                            backgroundColor: selectedSession === 'all' ? '#0068ff' : '#f0f0f0',
                                            color: selectedSession === 'all' ? 'white' : '#333',
                                            borderRadius: '4px',
                                            flex: 1,
                                            fontSize: '13px',
                                            textAlign: 'center',
                                            cursor: 'pointer',
                                            userSelect: 'none',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}
                                        onClick={() => handleSessionChange('all')}
                                        onTouchStart={(e) => {
                                            e.preventDefault();
                                            handleSessionChange('all');
                                            return false;
                                        }}
                                    >
                                        Tất cả
                                    </div>
                                    <div
                                        style={{
                                            padding: '5px 10px',
                                            backgroundColor: selectedSession === 'morning' ? '#0068ff' : '#f0f0f0',
                                            color: selectedSession === 'morning' ? 'white' : '#333',
                                            borderRadius: '4px',
                                            flex: 1,
                                            fontSize: '13px',
                                            textAlign: 'center',
                                            cursor: 'pointer',
                                            userSelect: 'none',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}
                                        onClick={() => handleSessionChange('morning')}
                                        onTouchStart={(e) => {
                                            e.preventDefault();
                                            handleSessionChange('morning');
                                            return false;
                                        }}
                                    >
                                        Buổi sáng
                                    </div>
                                    <div
                                        style={{
                                            padding: '5px 10px',
                                            backgroundColor: selectedSession === 'afternoon' ? '#0068ff' : '#f0f0f0',
                                            color: selectedSession === 'afternoon' ? 'white' : '#333',
                                            borderRadius: '4px',
                                            flex: 1,
                                            fontSize: '13px',
                                            textAlign: 'center',
                                            cursor: 'pointer',
                                            userSelect: 'none',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}
                                        onClick={() => handleSessionChange('afternoon')}
                                        onTouchStart={(e) => {
                                            e.preventDefault();
                                            handleSessionChange('afternoon');
                                            return false;
                                        }}
                                    >
                                        Buổi chiều
                                    </div>
                                </div>

                                <Box className="calendar-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '8px' }}>
                                    {['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'].map((day) => (
                                        <Text key={day} className="calendar-day-header" style={{ textAlign: 'center', fontSize: '12px', fontWeight: 500, color: '#666', marginBottom: '8px' }}>
                                            {day}
                                        </Text>
                                    ))}

                                    {(() => {
                                        // Kiểm tra nếu không có dữ liệu cho buổi học đã chọn
                                        if (selectedSession !== 'all' && stats && stats.recentAttendances) {
                                            // Kiểm tra xem có dữ liệu nào phù hợp với session đã chọn không
                                            const hasSessionData = stats.recentAttendances.some(att => {
                                                const attDate = new Date(att.date);
                                                return att.session === selectedSession &&
                                                    attDate.getMonth() === currentMonth &&
                                                    attDate.getFullYear() === currentYear;
                                            });

                                            if (!hasSessionData && stats.recentAttendances.length > 0) {
                                                console.log(`No data found for session ${selectedSession} in current month`);
                                                return (
                                                    <Box style={{
                                                        gridColumn: '1 / span 7',
                                                        textAlign: 'center',
                                                        padding: '30px 0',
                                                        color: '#666'
                                                    }}>
                                                        Không có dữ liệu điểm danh cho {selectedSession === 'morning' ? 'buổi sáng' : 'buổi chiều'} trong tháng này
                                                    </Box>
                                                );
                                            }
                                        }

                                        // Tính toán ngày đầu tháng và số ngày trong tháng
                                        const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
                                        const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
                                        const daysInMonth = lastDayOfMonth.getDate();

                                        // Tính offset cho ngày đầu tiên (0 = Chủ nhật, 1 = Thứ 2, ...)
                                        const firstDayOffset = firstDayOfMonth.getDay();

                                        // Tạo map các ngày có điểm danh từ dữ liệu API
                                        const attendanceDays = {};
                                        if (stats && stats.recentAttendances && stats.recentAttendances.length > 0) {
                                            // Log chi tiết về dữ liệu
                                            console.log(`Processing ${stats.recentAttendances.length} attendance records`);
                                            console.log(`Current filter: ${selectedSession}`);

                                            // Đếm số lượng bản ghi theo session
                                            const morningCount = stats.recentAttendances.filter(att => att.session === 'morning').length;
                                            const afternoonCount = stats.recentAttendances.filter(att => att.session === 'afternoon').length;
                                            console.log(`Morning records: ${morningCount}, Afternoon records: ${afternoonCount}`);

                                            stats.recentAttendances.forEach(att => {
                                                const attDate = new Date(att.date);
                                                // Chỉ xử lý các ngày trong tháng hiện tại
                                                if (attDate.getMonth() === currentMonth && attDate.getFullYear() === currentYear) {
                                                    // Nếu đang lọc theo buổi học, chỉ hiển thị dữ liệu của buổi đó
                                                    if (selectedSession === 'all' || att.session === selectedSession) {
                                                        const day = attDate.getDate();
                                                        if (!attendanceDays[day]) {
                                                            attendanceDays[day] = [];
                                                        }
                                                        attendanceDays[day].push(att);
                                                        console.log(`Added attendance for day ${day}, session: ${att.session}, status: ${att.status}`);
                                                    }
                                                }
                                            });
                                        }

                                        // Log để debug
                                        console.log(`Attendance days for ${selectedSession}:`, Object.keys(attendanceDays).length);

                                        // Tạo mảng các ngày để hiển thị
                                        const days = [];

                                        // Thêm các ô trống trước ngày đầu tiên của tháng
                                        for (let i = 0; i < firstDayOffset; i++) {
                                            days.push(
                                                <Box
                                                    key={`empty-${i}`}
                                                    style={{
                                                        textAlign: 'center',
                                                        padding: '8px 0',
                                                        fontSize: '14px',
                                                        color: '#ccc'
                                                    }}
                                                />
                                            );
                                        }

                                        // Thêm các ngày trong tháng
                                        for (let day = 1; day <= daysInMonth; day++) {
                                            const today = new Date();
                                            const isToday = day === today.getDate() &&
                                                currentMonth === today.getMonth() &&
                                                currentYear === today.getFullYear();

                                            // Xác định trạng thái điểm danh cho ngày này
                                            let status = '';
                                            let backgroundColor = 'transparent';
                                            let textColor = '#333';
                                            let borderStyle = isToday ? '2px solid #0068ff' : 'none';
                                            let statusText = '';

                                            if (attendanceDays[day]) {
                                                // Nếu có nhiều bản ghi điểm danh trong ngày, ưu tiên theo thứ tự: absent > late > present
                                                const statuses = attendanceDays[day].map(att => att.status);

                                                if (statuses.includes('absent')) {
                                                    status = 'absent';
                                                    borderStyle = '2px solid #f44336';
                                                    textColor = '#f44336';
                                                    statusText = 'Vắng mặt';
                                                } else if (statuses.includes('late')) {
                                                    status = 'late';
                                                    borderStyle = '2px solid #f9a825';
                                                    textColor = '#f9a825';
                                                    statusText = 'Đi trễ';
                                                } else if (statuses.includes('present')) {
                                                    status = 'present';
                                                    borderStyle = '2px solid #4caf50';
                                                    textColor = '#4caf50';
                                                    statusText = 'Có mặt';
                                                }

                                                // Thêm thông tin buổi học
                                                const sessions = attendanceDays[day].map(att => att.session);
                                                if (sessions.includes('morning') && sessions.includes('afternoon')) {
                                                    statusText += ' (Cả ngày)';
                                                } else if (sessions.includes('morning')) {
                                                    statusText += ' (Sáng)';
                                                } else if (sessions.includes('afternoon')) {
                                                    statusText += ' (Chiều)';
                                                }
                                            }

                                            days.push(
                                                <Box
                                                    key={`day-${day}`}
                                                    className={`calendar-day ${status}`}
                                                    style={{
                                                        aspectRatio: '1',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        flexDirection: 'column',
                                                        borderRadius: '50%',
                                                        fontSize: '14px',
                                                        cursor: attendanceDays[day] ? 'pointer' : 'default',
                                                        color: isToday ? '#0068ff' : textColor,
                                                        backgroundColor: isToday ? '#e8f0fe' : backgroundColor,
                                                        border: borderStyle,
                                                        position: 'relative'
                                                    }}
                                                    title={statusText}
                                                >
                                                    <Text>{day}</Text>
                                                    {attendanceDays[day] && (
                                                        <Box style={{
                                                            position: 'absolute',
                                                            bottom: '2px',
                                                            left: '50%',
                                                            transform: 'translateX(-50%)',
                                                            width: '4px',
                                                            height: '4px',
                                                            borderRadius: '50%',
                                                            backgroundColor: textColor
                                                        }} />
                                                    )}
                                                </Box>
                                            );
                                        }

                                        return days;
                                    })()}
                                </Box>
                            </Box>
                        )}
                        {/* Attendance History List */}
                        <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', margin: '15px 0 10px', color: '#333' }}>
                            Lịch sử điểm danh gần đây
                        </Text>
                        <Box className="attendance-list" style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                            {(() => {
                                // Kết hợp newAttendanceRecords và history, với newAttendanceRecords xuất hiện trước
                                const combinedHistory = [...newAttendanceRecords, ...(history || [])];

                                // Loại bỏ duplicate records dựa trên _id
                                const uniqueHistory = combinedHistory.filter((item, index, self) =>
                                    index === self.findIndex(record => record._id === item._id)
                                );

                                // Sắp xếp theo thời gian mới nhất trước
                                const sortedHistory = uniqueHistory.sort((a, b) =>
                                    new Date(b.date) - new Date(a.date)
                                );

                                return sortedHistory.length > 0 ? (
                                    sortedHistory.map((item) => (
                                        <Box key={item._id} className="attendance-item" style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                                            <Box className="attendance-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                                <Text className="attendance-date" style={{ fontWeight: 500, fontSize: '15px' }}>
                                                    {formatDate(item.date)}
                                                </Text>
                                                <Text
                                                    className={`attendance-status status-${item.status}`}
                                                    style={{
                                                        fontSize: '13px',
                                                        padding: '3px 8px',
                                                        borderRadius: '12px',
                                                        fontWeight: 500,
                                                        backgroundColor: item.status === 'present' ? '#e6f7e6' :
                                                            item.status === 'absent' ? '#ffebee' :
                                                                item.status === 'late' ? '#fff3cd' : '#f0f0f0',
                                                        color: item.status === 'present' ? '#4caf50' :
                                                            item.status === 'absent' ? '#f44336' :
                                                                item.status === 'late' ? '#f9a825' : '#666',
                                                    }}
                                                >
                                                    {item.status === 'present' ? 'Có mặt' :
                                                        item.status === 'absent' ? 'Vắng mặt' :
                                                            item.status === 'late' ? 'Đi trễ' :
                                                                item.status === 'rejected' ? 'Từ chối' : item.status}
                                                </Text>
                                            </Box>
                                            <Box className="attendance-info" style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginTop: '10px' }}>
                                                <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px' }}>
                                                    <Text className="info-label" style={{ color: '#666' }}>
                                                        Buổi học:
                                                    </Text>
                                                    <Text className="info-value" style={{ fontWeight: 500, color: '#333' }}>
                                                        {item.session === 'morning' ? 'Buổi sáng' : 'Buổi chiều'}
                                                    </Text>
                                                </Box>

                                                <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px' }}>
                                                    <Text className="info-label" style={{ color: '#666' }}>
                                                        Lớp:
                                                    </Text>
                                                    <Text className="info-value" style={{ fontWeight: 500, color: '#333' }}>
                                                        {item.class?.name || '10A1'}
                                                    </Text>
                                                </Box>

                                                <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px' }}>
                                                    <Text className="info-label" style={{ color: '#666' }}>
                                                        Địa điểm:
                                                    </Text>
                                                    <Text className="info-value" style={{ fontWeight: 500, color: '#333' }}>
                                                        {item.location || 'Phòng học A'}
                                                    </Text>
                                                </Box>

                                                {item.notes && (
                                                    <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px' }}>
                                                        <Text className="info-label" style={{ color: '#666' }}>
                                                            Ghi chú:
                                                        </Text>
                                                        <Text className="info-value" style={{ fontWeight: 500, color: '#333' }}>
                                                            {item.notes}
                                                        </Text>
                                                    </Box>
                                                )}
                                            </Box>
                                        </Box>
                                    ))
                                ) : (
                                    <Box style={{ textAlign: 'center', padding: '30px 20px', backgroundColor: 'white', borderRadius: '8px' }}>
                                        <Text style={{ color: '#666' }}>Chưa có lịch sử điểm danh</Text>
                                    </Box>
                                );
                            })()}
                        </Box>
                    </Box>
                )}

                {/* Tab Content - Thống kê */}
                {activeTab === 'stats' && (
                    <Box>
                        {statsLoading ? (
                            <LoadingIndicator />
                        ) : (
                            <>
                                {/* Stats Filter */}
                                <Box className="stats-filter" style={{
                                    backgroundColor: 'white',
                                    borderRadius: '8px',
                                    padding: '15px',
                                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                    marginBottom: '15px'
                                }}>
                                    <Text style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '15px', color: '#333' }}>
                                        Bộ lọc thống kê
                                    </Text>

                                    {/* Period Filter */}
                                    <Box style={{ marginBottom: '15px' }}>
                                        <Text style={{ fontSize: '13px', color: '#666', marginBottom: '8px' }}>
                                            Khoảng thời gian:
                                        </Text>
                                        <Box style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                                            <Box
                                                style={{
                                                    padding: '6px 12px',
                                                    backgroundColor: tempStatsFilter.period === 'semester' ? '#0068ff' : '#f0f0f0',
                                                    color: tempStatsFilter.period === 'semester' ? 'white' : '#333',
                                                    borderRadius: '16px',
                                                    fontSize: '12px',
                                                    cursor: 'pointer',
                                                    userSelect: 'none',
                                                    textAlign: 'center'
                                                }}
                                                onClick={() => handleStatsFilterChange('period', 'semester')}
                                            >
                                                Học kỳ này
                                            </Box>
                                            <Box
                                                style={{
                                                    padding: '6px 12px',
                                                    backgroundColor: tempStatsFilter.period === 'custom' ? '#0068ff' : '#f0f0f0',
                                                    color: tempStatsFilter.period === 'custom' ? 'white' : '#333',
                                                    borderRadius: '16px',
                                                    fontSize: '12px',
                                                    cursor: 'pointer',
                                                    userSelect: 'none',
                                                    textAlign: 'center'
                                                }}
                                                onClick={() => handleStatsFilterChange('period', 'custom')}
                                            >
                                                Tùy chọn
                                            </Box>
                                        </Box>
                                    </Box>

                                    {/* Custom Date Range */}
                                    {tempStatsFilter.period === 'custom' && (
                                        <DateRangeFilter
                                            startDate={tempStatsFilter.startDate}
                                            endDate={tempStatsFilter.endDate}
                                            onStartDateChange={(value) => handleStatsFilterChange('startDate', value)}
                                            onEndDateChange={(value) => handleStatsFilterChange('endDate', value)}
                                            onApply={handleApplyStatsFilter}
                                            onClear={clearStatsFilters}
                                        />
                                    )}
                                </Box>

                                <Box className="calendar-view" style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                                    <Text className="calendar-title" style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '15px' }}>
                                        Thống kê tổng hợp
                                        {tempStatsFilter.period === 'custom' && tempStatsFilter.startDate && tempStatsFilter.endDate && (
                                            <span style={{ fontSize: '14px', fontWeight: 'normal', color: '#666', marginLeft: '8px' }}>
                                                ({format(new Date(tempStatsFilter.startDate), 'dd/MM/yyyy')} - {format(new Date(tempStatsFilter.endDate), 'dd/MM/yyyy')})
                                            </span>
                                        )}
                                    </Text>

                                    {stats ? (
                                        <>
                                            <Box style={{
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                backgroundColor: '#f5f5f5',
                                                padding: '15px',
                                                borderRadius: '8px',
                                                marginBottom: '20px'
                                            }}>
                                                <Box style={{ textAlign: 'center' }}>
                                                    <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Tỷ lệ điểm danh</Text>
                                                    <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#0068ff' }}>{stats.attendanceRate}%</Text>
                                                </Box>
                                                <Box style={{ textAlign: 'center' }}>
                                                    <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Tổng số buổi</Text>
                                                    <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#333' }}>{stats.totalAttendances}</Text>
                                                </Box>
                                            </Box>

                                            <Box style={{ marginTop: '15px' }}>
                                                <Box style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                                    <Text>Tỷ lệ đi học đầy đủ:</Text>
                                                    <Text style={{ fontWeight: 500 }}>
                                                        {stats.statusCounts.total > 0
                                                            ? Math.round((stats.statusCounts.present / stats.statusCounts.total) * 100)
                                                            : 0}%
                                                    </Text>
                                                </Box>
                                                <Box className="progress-bar" style={{ height: '8px', backgroundColor: '#ddd', borderRadius: '4px', overflow: 'hidden' }}>
                                                    <Box className="progress-fill" style={{
                                                        width: `${stats.statusCounts.total > 0
                                                            ? Math.round((stats.statusCounts.present / stats.statusCounts.total) * 100)
                                                            : 0}%`,
                                                        height: '100%',
                                                        backgroundColor: '#4caf50'
                                                    }} />
                                                </Box>
                                            </Box>

                                            <Box style={{ marginTop: '15px' }}>
                                                <Box style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                                    <Text>Tỷ lệ đi trễ:</Text>
                                                    <Text style={{ fontWeight: 500 }}>
                                                        {stats.statusCounts.total > 0
                                                            ? Math.round((stats.statusCounts.late / stats.statusCounts.total) * 100)
                                                            : 0}%
                                                    </Text>
                                                </Box>
                                                <Box className="progress-bar" style={{ height: '8px', backgroundColor: '#ddd', borderRadius: '4px', overflow: 'hidden' }}>
                                                    <Box className="progress-fill" style={{
                                                        width: `${stats.statusCounts.total > 0
                                                            ? Math.round((stats.statusCounts.late / stats.statusCounts.total) * 100)
                                                            : 0}%`,
                                                        height: '100%',
                                                        backgroundColor: '#f9a825'
                                                    }} />
                                                </Box>
                                            </Box>

                                            <Box style={{ marginTop: '15px' }}>
                                                <Box style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                                    <Text>Tỷ lệ vắng mặt:</Text>
                                                    <Text style={{ fontWeight: 500 }}>
                                                        {stats.statusCounts.total > 0
                                                            ? Math.round((stats.statusCounts.absent / stats.statusCounts.total) * 100)
                                                            : 0}%
                                                    </Text>
                                                </Box>
                                                <Box className="progress-bar" style={{ height: '8px', backgroundColor: '#ddd', borderRadius: '4px', overflow: 'hidden' }}>
                                                    <Box className="progress-fill" style={{
                                                        width: `${stats.statusCounts.total > 0
                                                            ? Math.round((stats.statusCounts.absent / stats.statusCounts.total) * 100)
                                                            : 0}%`,
                                                        height: '100%',
                                                        backgroundColor: '#f44336'
                                                    }} />
                                                </Box>
                                            </Box>
                                        </>
                                    ) : (
                                        <Box style={{ textAlign: 'center', padding: '30px 0' }}>
                                            <Text style={{ color: '#666' }}>Chưa có dữ liệu thống kê</Text>
                                        </Box>
                                    )}
                                </Box>

                                <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', margin: '15px 0 10px', color: '#333' }}>
                                    Chi tiết thống kê theo tháng
                                </Text>
                                <Box className="attendance-list" style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                                    {stats?.monthStats?.length > 0 ? (
                                        stats.monthStats.map((item) => (
                                            <Box key={item.month} className="attendance-item" style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                                                <Box className="attendance-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                                    <Text className="attendance-date" style={{ fontWeight: 500, fontSize: '15px' }}>
                                                        {item.monthName}
                                                    </Text>
                                                    <Text
                                                        className="attendance-status"
                                                        style={{
                                                            fontSize: '13px',
                                                            padding: '3px 8px',
                                                            borderRadius: '12px',
                                                            backgroundColor: '#e6f7e6',
                                                            color: '#4caf50',
                                                            fontWeight: 500
                                                        }}
                                                    >
                                                        Tỷ lệ: {item.total > 0 ? Math.round((item.present / item.total) * 100) : 0}%
                                                    </Text>
                                                </Box>
                                                <Box className="attendance-info" style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginTop: '10px' }}>
                                                    <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px', alignItems: 'center' }}>
                                                        <Text className="info-label" style={{ color: '#666' }}>
                                                            Có mặt:
                                                        </Text>
                                                        <Text className="info-value" style={{ fontWeight: 500, color: '#4caf50' }}>
                                                            {item.present}/{item.total} ngày
                                                        </Text>
                                                    </Box>
                                                    <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px', alignItems: 'center' }}>
                                                        <Text className="info-label" style={{ color: '#666' }}>
                                                            Đi trễ:
                                                        </Text>
                                                        <Text className="info-value" style={{ fontWeight: 500, color: item.late > 0 ? '#f9a825' : '#333' }}>
                                                            {item.late} lần
                                                        </Text>
                                                    </Box>
                                                    <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px', alignItems: 'center' }}>
                                                        <Text className="info-label" style={{ color: '#666' }}>
                                                            Vắng mặt:
                                                        </Text>
                                                        <Text className="info-value" style={{ fontWeight: 500, color: item.absent > 0 ? '#f44336' : '#333' }}>
                                                            {item.absent} lần
                                                        </Text>
                                                    </Box>
                                                    <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px', alignItems: 'center' }}>
                                                        <Text className="info-label" style={{ color: '#666' }}>
                                                            Từ chối:
                                                        </Text>
                                                        <Text className="info-value" style={{ fontWeight: 500, color: item.rejected > 0 ? '#f44336' : '#333' }}>
                                                            {item.rejected} lần
                                                        </Text>
                                                    </Box>
                                                </Box>
                                            </Box>
                                        ))
                                    ) : (
                                        <Box style={{ textAlign: 'center', padding: '20px', backgroundColor: 'white', borderRadius: '8px' }}>
                                            <Text style={{ color: '#666' }}>Chưa có dữ liệu thống kê</Text>
                                        </Box>
                                    )}
                                </Box>
                            </>
                        )}
                    </Box>
                )}

                {/* Tab Content - Xin phép */}
                {activeTab === 'leave' && (
                    <Box>
                        {leaveLoading ? (
                            <LoadingIndicator />
                        ) : (
                            <>
                                <Box className="leave-header" style={{
                                    backgroundColor: 'white',
                                    borderRadius: '8px',
                                    padding: '15px',
                                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                    marginBottom: '15px'
                                }}>
                                    <Text style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '15px' }}>
                                        Yêu cầu xin phép
                                    </Text>
                                    <Text style={{ color: '#666', marginBottom: '15px' }}>
                                        Gửi yêu cầu xin phép nghỉ học đến giáo viên chủ nhiệm
                                    </Text>
                                    <Button
                                        style={{
                                            backgroundColor: '#4caf50',
                                            color: 'white',
                                            width: '100%',
                                            height: '48px',
                                            fontSize: '16px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            gap: '8px'
                                        }}
                                        onClick={handleLeaveRequest}
                                    >
                                        <span style={{ fontSize: '20px' }}>{ICONS.EDIT}</span>
                                        <span>Tạo yêu cầu xin phép mới</span>
                                    </Button>
                                </Box>

                                {/* Filter Box */}
                                <Box className="leave-filter" style={{
                                    backgroundColor: 'white',
                                    borderRadius: '8px',
                                    padding: '15px',
                                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                    marginBottom: '15px'
                                }}>
                                    <Text style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '15px', color: '#333' }}>
                                        Bộ lọc yêu cầu
                                    </Text>

                                    {/* Status Filter */}
                                    <Box style={{ marginBottom: '15px' }}>
                                        <Text style={{ fontSize: '13px', color: '#666', marginBottom: '8px' }}>
                                            Trạng thái:
                                        </Text>
                                        <Box style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                                            {[
                                                { value: 'all', label: 'Tất cả' },
                                                { value: 'pending', label: 'Chờ duyệt' },
                                                { value: 'approved', label: 'Đã duyệt' },
                                                { value: 'rejected', label: 'Từ chối' }
                                            ].map((status) => (
                                                <Box
                                                    key={status.value}
                                                    style={{
                                                        padding: '6px 12px',
                                                        backgroundColor: leaveFilter.status === status.value ? '#0068ff' : '#f0f0f0',
                                                        color: leaveFilter.status === status.value ? 'white' : '#333',
                                                        borderRadius: '16px',
                                                        fontSize: '12px',
                                                        cursor: 'pointer',
                                                        userSelect: 'none',
                                                        minWidth: '60px',
                                                        textAlign: 'center'
                                                    }}
                                                    onClick={() => handleLeaveFilterChange('status', status.value)}
                                                >
                                                    {status.label}
                                                </Box>
                                            ))}
                                        </Box>
                                    </Box>

                                    {/* Date Range Filter */}
                                    <DateRangeFilter
                                        startDate={tempLeaveFilter.startDate}
                                        endDate={tempLeaveFilter.endDate}
                                        onStartDateChange={(value) => handleLeaveFilterChange('startDate', value)}
                                        onEndDateChange={(value) => handleLeaveFilterChange('endDate', value)}
                                        onApply={handleApplyLeaveFilter}
                                        onClear={clearLeaveFilters}
                                    />
                                </Box>

                                <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', margin: '15px 0 10px', color: '#333' }}>
                                    Danh sách yêu cầu
                                    {leaveFilter.status !== 'all' && (
                                        <span style={{ fontSize: '14px', fontWeight: 'normal', color: '#666', marginLeft: '8px' }}>
                                            ({leaveFilter.status === 'pending' ? 'Chờ duyệt' :
                                                leaveFilter.status === 'approved' ? 'Đã duyệt' : 'Từ chối'})
                                        </span>
                                    )}
                                </Text>

                                <Box className="leave-list" style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                                    {leaveRequests && leaveRequests.length > 0 ? (
                                        leaveRequests.map((item) => (
                                            <Box key={item._id} className="leave-item" style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                                                <Box className="leave-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                                    <Text className="leave-date" style={{ fontWeight: 500, fontSize: '15px' }}>
                                                        {format(new Date(item.startDate), 'dd/MM/yyyy')}
                                                        {item.startDate !== item.endDate && ` - ${format(new Date(item.endDate), 'dd/MM/yyyy')}`}
                                                    </Text>
                                                    <Text
                                                        className="leave-status"
                                                        style={{
                                                            fontSize: '13px',
                                                            padding: '3px 8px',
                                                            borderRadius: '12px',
                                                            backgroundColor: item.status === 'approved' ? '#e6f7e6' :
                                                                item.status === 'rejected' ? '#ffebee' : '#fff3cd',
                                                            color: item.status === 'approved' ? '#4caf50' :
                                                                item.status === 'rejected' ? '#f44336' : '#f9a825',
                                                            fontWeight: 500
                                                        }}
                                                    >
                                                        {item.status === 'approved' ? 'Đã duyệt' :
                                                            item.status === 'rejected' ? 'Từ chối' : 'Đang chờ'}
                                                    </Text>
                                                </Box>
                                                <Box className="leave-info" style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginTop: '10px' }}>
                                                    <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px' }}>
                                                        <Text className="info-label" style={{ color: '#666' }}>
                                                            Buổi học:
                                                        </Text>
                                                        <Text className="info-value" style={{ fontWeight: 500, color: '#333' }}>
                                                            {item.sessions.includes('all-day') ? 'Cả ngày' :
                                                                item.sessions.includes('morning') ? 'Buổi sáng' : 'Buổi chiều'}
                                                        </Text>
                                                    </Box>
                                                    <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px' }}>
                                                        <Text className="info-label" style={{ color: '#666' }}>
                                                            Lớp:
                                                        </Text>
                                                        <Text className="info-value" style={{ fontWeight: 500, color: '#333' }}>
                                                            {item.class?.name || '10A1'}
                                                        </Text>
                                                    </Box>
                                                    <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px' }}>
                                                        <Text className="info-label" style={{ color: '#666' }}>
                                                            Lý do:
                                                        </Text>
                                                        <Text className="info-value" style={{ fontWeight: 500, color: '#333', maxWidth: '70%', textAlign: 'right' }}>
                                                            {item.reason}
                                                        </Text>
                                                    </Box>
                                                </Box>
                                                {/* Chỉ hiển thị nút xóa cho yêu cầu pending */}
                                                {item.status === 'pending' && (
                                                    <Box style={{ marginTop: '15px', display: 'flex', justifyContent: 'flex-end' }}>
                                                        <Button
                                                            style={{
                                                                backgroundColor: '#f44336',
                                                                color: 'white',
                                                                padding: '8px 12px',
                                                                fontSize: '14px',
                                                                borderRadius: '4px'
                                                            }}
                                                            onClick={() => deleteLeaveRequest(item._id)}
                                                        >
                                                            Hủy yêu cầu
                                                        </Button>
                                                    </Box>
                                                )}

                                                {/* Hiển thị thông tin bổ sung cho yêu cầu đã được xử lý */}
                                                {item.status !== 'pending' && item.reviewedAt && (
                                                    <Box style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '6px' }}>
                                                        <Text style={{ fontSize: '13px', color: '#666' }}>
                                                            {item.status === 'approved' ? 'Đã duyệt' : 'Đã từ chối'} lúc: {format(new Date(item.reviewedAt), 'dd/MM/yyyy HH:mm')}
                                                        </Text>
                                                        {item.reviewNote && (
                                                            <Text style={{ fontSize: '13px', color: '#666', marginTop: '5px' }}>
                                                                Ghi chú: {item.reviewNote}
                                                            </Text>
                                                        )}
                                                    </Box>
                                                )}
                                            </Box>
                                        ))
                                    ) : (
                                        <Box style={{ textAlign: 'center', padding: '30px 20px', backgroundColor: 'white', borderRadius: '8px' }}>
                                            <Text style={{ color: '#666' }}>Chưa có yêu cầu xin phép nào</Text>
                                        </Box>
                                    )}
                                </Box>
                            </>
                        )}
                    </Box>
                )}
            </Box>

            {/* Modal Xin phép */}
            <Modal
                visible={leaveModalVisible}
                title="Tạo yêu cầu xin phép"
                onClose={() => setLeaveModalVisible(false)}
                actions={[]}
                className="leave-request-modal"
            >
                <LeaveRequestForm
                    onSubmit={({ data, error }) => {
                        if (error) {
                            notification.showError('Thông báo', error);
                            return;
                        }
                        submitLeaveRequest(data);
                    }}
                    onCancel={() => setLeaveModalVisible(false)}
                    initialData={{
                        startDate: format(new Date(), 'yyyy-MM-dd'),
                        endDate: format(new Date(), 'yyyy-MM-dd'),
                        sessions: ['all-day'],
                        reason: '',
                        attachments: []
                    }}
                    onChange={setLeaveFormData}
                    loading={leaveLoading}
                    role="student"
                />
            </Modal>

            {/* Modal Xác nhận */}
            <Modal
                visible={confirmModal.visible}
                title={confirmModal.title}
                onClose={closeConfirmModal}
                actions={[
                    ...(confirmModal.cancelText ? [{
                        text: confirmModal.cancelText,
                        onClick: closeConfirmModal,
                        style: { backgroundColor: '#f5f5f5', color: '#333' }
                    }] : []),
                    ...(confirmModal.confirmText ? [{
                        text: confirmModal.confirmText,
                        onClick: () => {
                            if (confirmModal.onConfirm) {
                                confirmModal.onConfirm();
                            }
                        },
                        style: {
                            backgroundColor: confirmModal.confirmText === 'Xóa' ? '#f44336' : '#0068ff',
                            color: 'white'
                        }
                    }] : [])
                ]}
            >
                <Box style={{ padding: '30px 20px', textAlign: 'center' }}>
                    {confirmModal.title === 'Xác nhận xóa' && (
                        <Text style={{ fontSize: '60px', marginBottom: '25px', color: '#f44336' }}>{ICONS.DELETE}</Text>
                    )}
                    {confirmModal.title === 'Thành công' && (
                        <Text style={{ fontSize: '60px', marginBottom: '25px', color: '#4caf50' }}>{ICONS.DONE}</Text>
                    )}
                    {confirmModal.title === 'Lỗi' && (
                        <Text style={{ fontSize: '60px', marginBottom: '25px', color: '#f44336' }}>{ICONS.ERROR}</Text>
                    )}
                    {confirmModal.title === 'Thông báo' && (
                        <Text style={{ fontSize: '60px', marginBottom: '25px', color: '#0068ff' }}>{ICONS.INFO}</Text>
                    )}
                    <Text style={{ fontSize: '16px', marginBottom: '15px', lineHeight: '1.5' }}>{confirmModal.message}</Text>
                </Box>
            </Modal>

            <BottomNavigationEdu active="attendance" />
        </Box>
    );
};

export default Attendance;